# OneEPR Project

## Overview
This monorepo contains the complete OneEPR application stack with **1 backend** service and **3 frontend** applications, all containerized with Docker for seamless deployment.

## Projects

### 🚀 Backend
**Location:** `backend/`  
**Tech Stack:** Java 21, Spring Boot 3.3.4, PostgreSQL, Maven  
**Features:** REST API, OAuth2, JPA, Flyway migrations, OpenAPI documentation  
**Key Libraries:** Spring Security, Lombok, MapStruct, Apache POI, AWS SDK

### 🎯 Frontend Applications

#### 1. Admin Portal
**Location:** `frontend/admin-portal/`  
**Tech Stack:** Next.js 14, React 18, <PERSON>Script, Tailwind CSS  
**Package Manager:** pnpm  
**Key Features:** Admin dashboard, React Query, Radix UI components, Mapbox integration  
**Testing:** Jest, Cy<PERSON>, Playwright

#### 2. Clerk Portal  
**Location:** `frontend/clerk-portal/`  
**Tech Stack:** Next.js 14, React 18, <PERSON><PERSON>, Tailwind CSS  
**Package Manager:** pnpm  
**Key Features:** Document management, PDF generation, Excel processing, i18n support  
**Authentication:** NextAuth.js

#### 3. Shop Customer Portal
**Location:** `frontend/shop-customer-portal/`  
**Tech Stack:** Next.js 14, React 18, TypeScript, Tailwind CSS  
**Package Manager:** pnpm  
**Key Features:** E-commerce storefront, Stripe payments, Mapbox maps, onboarding tours  
**CMS:** Strapi integration

## Quick Start
```bash
# Start all services
docker-compose up

# Backend only
cd backend && ./mvnw spring-boot:run

# Frontend development
cd frontend/[portal-name] && pnpm dev
```

## Architecture
- **Microservices:** Containerized services with Docker
- **API-First:** RESTful backend with OpenAPI documentation  
- **Modern Frontend:** React-based SPAs with server-side rendering
- **Database:** PostgreSQL with Flyway migrations
- **Authentication:** OAuth2 + JWT tokens
