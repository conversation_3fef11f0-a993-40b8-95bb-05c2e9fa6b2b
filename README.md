# MZS Connector

This will be a microservice to provide a database for MZS (central place for german packaging licensing data transfer).

## 🏗️ Project Structure

```
mzs-connector/
├── backend/                 # Spring Boot application
├── docker/                  # Docker orchestration and utilities
├── .gitlab-ci.yml          # GitLab CI/CD pipeline configuration
├── .gitignore              # Git ignore patterns
└── README.md               # This file
```

## 🚀 Quick Start

### Prerequisites

- Java 21
- Maven 3.6+
- Docker & Docker Compose
- PostgreSQL (for production)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mzs-connector
   ```

2. **Start the database**
   ```bash
   cd docker
   ./start-db
   ```

3. **Run the backend application**
   ```bash
   cd backend
   ./mvnw spring-boot:run -Dspring-boot.run.profiles=local
   ```

### Docker Development

1. **Start all services**
   ```bash
   cd docker
   ./start-be
   ```

2. **Access the application**
   - Backend API: http://localhost:8080
   - Mailpit (Email testing): http://localhost:8025

## 📁 Components

### Backend
- **Technology**: Spring Boot 3.3.4 with Java 21
- **Database**: PostgreSQL with Flyway migrations
- **Testing**: JUnit 5 with H2 in-memory database
- **Documentation**: See [backend/README.md](backend/README.md)

### Docker
- **Orchestration**: Docker Compose for multi-service setup
- **Services**: Backend application, PostgreSQL, Mailpit
- **Utilities**: Database management scripts
- **Documentation**: See [docker/README.md](docker/README.md)

### CI/CD
- **Platform**: GitLab CI/CD
- **Stages**: Build, Test, Security Scanning, Deployment
- **Features**: SonarQube integration, automated testing

## 🛠️ Development Workflow

### Environment Profiles

- **local**: Local development with PostgreSQL on localhost
- **docker-stack**: Containerized development environment
- **test**: Testing with H2 in-memory database
- **pipeline**: CI/CD pipeline configuration

### Database Management

```bash
# Start database
cd docker && ./start-db

# Clean database
cd backend/scripts && ./clean_db.sh

# Create database dump
cd backend/scripts && ./create_db_dump.sh

# Restore from dump
cd backend/scripts && ./restore_from_db_dump.sh
```

### Testing

```bash
# Run unit tests
cd backend && ./mvnw test

# Run with specific profile
cd backend && ./mvnw test -Dspring.profiles.active=test
```

## 🔧 Configuration

### Application Properties

- `backend/src/main/resources/application.properties` - Base configuration
- `backend/src/main/resources/application-local.properties` - Local development
- `backend/src/main/resources/application-docker-stack.properties` - Docker environment
- `backend/src/test/resources/application-*.properties` - Test configurations

### Docker Configuration

- `docker/docker-compose.yml` - Main orchestration file
- `docker/common` - Shared environment variables
- `backend/Dockerfile*` - Application container definitions

## 📋 Available Scripts

### Docker Scripts
- `./start-db` - Start PostgreSQL database
- `./start-be` - Start backend with database
- `./start-fe` - Start frontend services
- `./start-mailpit` - Start email testing service
- `./stop` - Stop all services
- `./flush-db` - Reset database
- `./rebuild-*` - Rebuild specific services

### Backend Scripts
- `./mvnw spring-boot:run` - Run application
- `./mvnw test` - Run tests
- `./mvnw clean package` - Build application

## 🚦 CI/CD Pipeline

The GitLab CI/CD pipeline includes:

1. **Build Stage**: Maven compilation and packaging
2. **Test Stage**: Unit tests and SonarQube analysis
3. **Security Stage**: SAST and secret detection
4. **Deploy Stage**: Containerized deployment

## 🤝 Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Run tests locally
4. Submit a merge request

## 🆘 Support

For support and questions:
- Check the component-specific README files
- Review the GitLab CI/CD logs
- Consult the application logs in `backend/logs/`

## 🔗 Related Documentation

- [Backend Documentation](backend/README.md)
- [Docker Documentation](docker/README.md)
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [Docker Documentation](https://docs.docker.com/)