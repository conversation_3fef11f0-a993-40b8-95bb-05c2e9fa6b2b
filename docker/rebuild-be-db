#!/usr/bin/env bash
source ./common

printf "\n=> Shutting down the backend and removing the db volume\n\n"
docker-compose down -v

check_error "=> Maybe a full backend rebuild is needed.\n   Try running ./rebuild-be-full\n"

printf "\n=> Restarting backend\n\n"
docker-compose up -d mzs-connector-backend

check_error "=> Maybe a full backend rebuild is needed.\n   Try running ./rebuild-be-full\n"

print_success "\n=> Backend should be running at http://localhost:8080\n\n"

exit 0
