#!/usr/bin/env bash
source ./common

docker-compose up -d --build --force-recreate mzs-connector-frontend-shop mzs-connector-frontend-clerk-portal mzs-connector-frontend-admin-portal

check_error "$DEFAULT_WARNING_MESSAGE"

print_success "\n=> Shop frontend should be running at http://localhost:3000\n\n"
print_success "\n=> Clerk frontend should be running at http://localhost:3001\n\n"
print_success "\n=> Admin frontend should be running at http://localhost:3002\n\n"

exit 0
