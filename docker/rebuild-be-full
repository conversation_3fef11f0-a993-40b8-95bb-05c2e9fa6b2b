#!/usr/bin/env bash
source ./common

printf "\n=> Shutting down the backend\n\n"
docker-compose down -v mzs-connector-backend

check_error "$DEFAULT_WARNING_MESSAGE"

echo "Rebuilding and restarting backend"
docker-compose up -d --build --force-recreate mzs-connector-backend

check_error "$DEFAULT_WARNING_MESSAGE"

print_success "\n=> The backend should be running at http://localhost:8080"
print_success "\n=> The Swagger UI should be available at http://localhost:8080/swagger-ui/index.html\n\n"

exit 0
