services:
  mzs-connector-db:
    image: postgres:17.3
    restart: always
    environment:
      PGUSER: mzs-connector-local-user
      POSTGRES_USER: mzs-connector-local-user
      POSTGRES_PASSWORD: mzs-connector-local-password
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 1s
      timeout: 5s
      retries: 10
    ports:
      - 5432:5432

  mzs-connector-backend:
    build:
      context: ../backend
      dockerfile: ../docker/build/backend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    depends_on:
      mzs-connector-db:
        condition: service_healthy
      mzs-connector-mailpit:
        condition: service_started
    env_file:
      - ./build/backend/.env
    ports:
      - 8080:8080

  mzs-connector-frontend-shop:
    build:
      context: ../frontend/shop-customer-portal
      dockerfile: ../../docker/build/frontend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    env_file:
      - ./build/frontend/.env_shop
    ports:
      - 3000:3000

  mzs-connector-frontend-clerk-portal:
    build:
      context: ../frontend/clerk-portal
      dockerfile: ../../docker/build/frontend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    env_file:
      - ./build/frontend/.env_clerk_portal
    ports:
      - 3001:3000

  mzs-connector-frontend-admin-portal:
    build:
      context: ../frontend/admin-portal
      dockerfile: ../../docker/build/frontend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    env_file:
      - ./build/frontend/.env_admin_portal
    ports:
      - 3002:3000

  mzs-connector-mailpit:
    image: axllent/mailpit:v1.22
    restart: always
    ports:
      - 1025:1025
      - 8025:8025

volumes:
  pgdata:
