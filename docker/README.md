# Docker - Container Orchestration

Docker-based development environment with PostgreSQL database, backend application, and supporting services.

## 🏗️ Architecture

### Services Overview
- **PostgreSQL**: Primary database server
- **Backend**: Spring Boot application container
- **Mailpit**: Email testing and debugging
- **Frontend**: (Placeholder for future frontend services)

### Network Configuration
- **Network**: Custom Docker network for service communication
- **DNS**: Services accessible by container names
- **Ports**: Exposed ports for local development access

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+

### Starting Services

```bash
# Start database only
./start-db

# Start backend with database
./start-be

# Start all services including frontend
./start-fe

# Start email testing service
./start-mailpit

# Stop all services
./stop
```

## 📋 Available Scripts

### Service Management

#### `./start-db`
Starts PostgreSQL database container
- **Port**: 5432
- **Database**: postgres
- **User**: mzs-connector-local-user
- **Password**: mzs-connector-local-password

#### `./start-be`
Starts backend application with database
- **Backend Port**: 8080
- **Database Port**: 5432
- **Profile**: docker-stack

#### `./start-fe`
Starts frontend services (placeholder)
- Includes all backend services
- Ready for frontend integration

#### `./start-mailpit`
Starts email testing service
- **Web UI**: http://localhost:8025
- **SMTP**: localhost:1025
- **Purpose**: Email debugging and testing

#### `./stop`
Stops all running containers
- Graceful shutdown
- Preserves data volumes
- Cleans up networks

### Database Management

#### `./flush-db`
Resets database to clean state
- Stops database container
- Removes data volume
- Restarts with fresh database

#### `./rebuild-be-db`
Rebuilds backend and database
- Stops services
- Rebuilds backend container
- Restarts with fresh database

#### `./rebuild-be-full`
Complete backend rebuild
- Removes backend container and image
- Rebuilds from scratch
- Restarts all services

#### `./rebuild-fe`
Frontend rebuild (placeholder)
- Ready for frontend service integration

## 🔧 Configuration

### Docker Compose Structure

```yaml
# docker-compose.yml
services:
  mzs-connector-db:      # PostgreSQL database
  mzs-connector-be:      # Spring Boot backend
  mailpit:             # Email testing service
```

### Environment Variables

Located in `common` file:
```bash
# Database configuration
POSTGRES_DB=postgres
POSTGRES_USER=mzs-connector-local-user
POSTGRES_PASSWORD=mzs-connector-local-password

# Application configuration
SPRING_PROFILES_ACTIVE=docker-stack
```

### Volume Mounts
- **Database**: Persistent data storage
- **Backend**: Source code for development
- **Logs**: Application log persistence

## 🗄️ Database Configuration

### PostgreSQL Container
- **Image**: postgres:latest
- **Port**: 5432 (host) → 5432 (container)
- **Volume**: `mzs-connector-db-data`
- **Network**: mzs-connector-network

### Connection Details
```
Host: localhost (from host) / mzs-connector-db (from containers)
Port: 5432
Database: postgres
Username: mzs-connector-local-user
Password: mzs-connector-local-password
```

### Data Persistence
- **Volume**: `mzs-connector-db-data`
- **Location**: Docker managed volume
- **Backup**: Use database scripts in `../backend/scripts/`

## 🏃‍♂️ Backend Container

### Container Configuration
- **Base Image**: openjdk:21-jdk
- **Port**: 8080 (host) → 8080 (container)
- **Profile**: docker-stack
- **Dependencies**: mzs-connector-db

### Development Mode
- **Volume Mount**: Source code mounted for hot reload
- **Debugging**: JVM debug port available
- **Logs**: Real-time log streaming

### Build Process
```bash
# Development build
docker build -f ../backend/Dockerfile.build -t mzs-connector-be .

# Production build  
docker build -f ../backend/Dockerfile.prod -t mzs-connector-be .
```

## 📧 Email Testing (Mailpit)

### Service Details
- **Image**: axllent/mailpit
- **Web UI**: http://localhost:8025
- **SMTP**: localhost:1025
- **Purpose**: Capture and display emails

### Integration
- Backend configured to use Mailpit SMTP
- All emails captured and displayed in web UI
- No external email dependencies

### Usage
1. Start Mailpit: `./start-mailpit`
2. Configure application to use localhost:1025
3. Access web UI at http://localhost:8025
4. View captured emails in real-time

## 🔍 Monitoring & Debugging

### Container Logs
```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f mzs-connector-be
docker-compose logs -f mzs-connector-db

# View recent logs
docker-compose logs --tail=100 mzs-connector-be
```

### Container Status
```bash
# Check running containers
docker-compose ps

# Check container health
docker-compose exec mzs-connector-be curl http://localhost:8080/actuator/health

# Access container shell
docker-compose exec mzs-connector-be bash
docker-compose exec mzs-connector-db psql -U mzs-connector-local-user -d postgres
```

### Network Debugging
```bash
# Inspect network
docker network inspect docker_mzs-connector-network

# Test connectivity between containers
docker-compose exec mzs-connector-be ping mzs-connector-db
```

## 🛠️ Development Workflow

### Typical Development Session

1. **Start Database**
   ```bash
   ./start-db
   ```

2. **Develop Backend**
   ```bash
   # Option 1: Run in container
   ./start-be
   
   # Option 2: Run locally, use containerized DB
   cd ../backend && ./mvnw spring-boot:run -Dspring-boot.run.profiles=docker-stack
   ```

3. **Test Email Features**
   ```bash
   ./start-mailpit
   # Access http://localhost:8025
   ```

4. **Clean Up**
   ```bash
   ./stop
   ```

### Database Development

```bash
# Fresh database
./flush-db

# Access database directly
docker-compose exec mzs-connector-db psql -U mzs-connector-local-user -d postgres

# Backup database
cd ../backend/scripts && ./create_db_dump.sh

# Restore database
cd ../backend/scripts && ./restore_from_db_dump.sh
```

## 🔧 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Check what's using the port
lsof -i :5432
lsof -i :8080

# Stop conflicting services
./stop
```

**Database Connection Issues**
```bash
# Check database container status
docker-compose ps mzs-connector-db

# Check database logs
docker-compose logs mzs-connector-db

# Restart database
docker-compose restart mzs-connector-db
```

**Backend Won't Start**
```bash
# Check backend logs
docker-compose logs mzs-connector-be

# Rebuild backend container
./rebuild-be-full

# Check database connectivity
docker-compose exec mzs-connector-be ping mzs-connector-db
```

**Volume Issues**
```bash
# List volumes
docker volume ls

# Remove specific volume (data loss!)
docker volume rm docker_mzs-connector-db-data

# Recreate with fresh data
./flush-db
```

### Performance Issues

**Slow Database**
- Check available disk space
- Monitor container resource usage
- Consider increasing Docker memory allocation

**Slow Backend Startup**
- Check JVM memory settings
- Monitor container logs for errors
- Verify database connectivity

## 📁 File Structure

```
docker/
├── docker-compose.yml      # Main orchestration file
├── common                  # Environment variables
├── README.md              # This file
├── start-db               # Database startup script
├── start-be               # Backend startup script
├── start-fe               # Frontend startup script
├── start-mailpit          # Email service startup script
├── stop                   # Stop all services script
├── flush-db               # Database reset script
├── rebuild-be-db          # Backend + DB rebuild script
├── rebuild-be-full        # Full backend rebuild script
└── rebuild-fe             # Frontend rebuild script
```

## 🚀 Production Considerations

### Security
- Change default passwords
- Use secrets management
- Implement proper network isolation
- Enable container security scanning

### Performance
- Use multi-stage Docker builds
- Optimize container resource limits
- Implement health checks
- Use production-grade PostgreSQL configuration

### Monitoring
- Add container monitoring (Prometheus/Grafana)
- Implement log aggregation
- Set up alerting for critical services
- Monitor resource usage

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Hub](https://hub.docker.com/_/postgres)
- [Mailpit Documentation](https://github.com/axllent/mailpit)