#!/usr/bin/env bash
source ./common

docker-compose up -d mzs-connector-backend

check_error "=> Maybe the database needs to be rebuilt.\n   Try running ./rebuild-be-db. If this doesn't help, try ./rebuild-be-full\n"

print_success "\n=> The backend should be running at http://localhost:8080"
print_success "\n=> The Swagger UI should be available at http://localhost:8080/swagger-ui/index.html\n\n"

exit 0
