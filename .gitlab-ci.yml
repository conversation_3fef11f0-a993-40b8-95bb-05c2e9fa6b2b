# ----------------- GLOBAL CONFIGURATIONS ----------------- #
default:
  tags: ["aws"]
  cache:
    key: ${CI_PROJECT_NAME}-pnpm-store
    paths:
      - .pnpm-store

image: gitlab.interzero.de/software-development/dependency_proxy/containers/node:${NODE_VERSION}-alpine

include:
  - project: "devops-templates/ci-templates"
    ref: main
    file: "/teams/notifications.yml"
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Jobs/Secret-Detection.gitlab-ci.yml
  - local: backend/.gitlab-ci.yml

stages:
  - install_dependencies
  - test
  - build_docker_image
  - deploy
  - teams_notifications
  - cleanup

# Global variables
variables:
  SHORT_PROJECT_NAME: mzs-connector
  PROJECT_NAME: mzs-connector
  COMPONENT: be
  NEXUS_PROD: 6000
  NEXUS_ENTW_QAT: 5000
  BRANCH: ${CI_COMMIT_REF_NAME}

before_script:
  - export BRANCH_TAG=$(echo ${CI_COMMIT_REF_NAME#*/} | tr '_' '-' | sed 's/[^a-zA-Z0-9-]//g')

# ----------------- SONAR ----------------- #
be_sonarqube-check:
  stage: test
  image: nexus.interzero.de:5000/maven:3.9.5-openjdk-21
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - cd backend
    - mvn verify sonar:sonar
  allow_failure: true
  tags: [ "eks-entw-qat-with-s3" ]
  only:
    - merge_requests

# ----------------- NOTIFY ----------------- #
teams_notifications_success:
  stage: teams_notifications
  when: on_success
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
  before_script:
    - |
      if [ -n "$CI_ENVIRONMENT_URL" ]; then
        ICON="✅ $CI_ENVIRONMENT_URL"
      else
        ICON="✅"
      fi
  variables:
    STATUS: "success"
    COMPONENT: "MonoRepo"
  extends:
    - .pipeline_succeed_aws

teams_notifications_failed:
  stage: teams_notifications
  when: on_failure
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
  before_script:
    - |
      if [ -n "$CI_ENVIRONMENT_URL" ]; then
        ICON="❌ $CI_ENVIRONMENT_URL"
      else
        ICON="❌"
      fi
  variables:
    STATUS: "failed"
    COMPONENT: "MonoRepo"
  extends:
    - .pipeline_failed_aws
