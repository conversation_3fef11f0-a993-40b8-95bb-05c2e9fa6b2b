#!/bin/sh

if [ "$#" -ne 6 ]; then
    echo "Usage: $0 <source_database> <target_database> <db_user> <db_password> <db_host> <db_port>"
    exit 1
fi

SOURCE_DB=$1
TARGET_DB=$2
DB_USER=$3
DB_PASSWORD=$4
DB_HOST=$5
DB_PORT=$6

echo "Checking if the target database '$TARGET_DB' exists..."

export PGPASSWORD=$DB_PASSWORD

# Check if the target database exists
DB_EXISTS=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$TARGET_DB'")

if [ "$DB_EXISTS" = "1" ]; then
    echo "Database '$TARGET_DB' already exists. Exiting with success."
    exit 0
fi

echo "Creating a copy of the database from '$SOURCE_DB' to '$TARGET_DB'..."

# Terminate active connections to the target database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_activity 
WHERE pg_stat_activity.datname = '$TARGET_DB' 
AND pid <> pg_backend_pid();"

# Drop the target database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS \"$TARGET_DB\";"

# Create a new, empty target database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "CREATE DATABASE \"$TARGET_DB\" OWNER \"$DB_USER\";"

# Dump the source database to a file
pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$SOURCE_DB" > /tmp/source_db_dump.sql

# Restore the dump to the new target database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -f /tmp/source_db_dump.sql

if [ $? -eq 0 ]; then
    echo "Database copy from '$SOURCE_DB' to '$TARGET_DB' created successfully."
else
    echo "Failed to create database copy from '$SOURCE_DB' to '$TARGET_DB'."
    exit 1
fi
