#!/usr/bin/env bash

# Function to get the execution code of the last command,
# if it is different from 0, print an error message and exit
function check_error {
    if [ $? -ne 0 ]; then
        # Print an error message in red
        print_error "\n=====> ❗ THERE WAS AN ERROR <====="
        print_error "=> Please check the error message above and try to fix it.\n\n"

        # If the function was called with an argument, print it
        # Concatenate all the arguments in a single string
        if [ $# -gt 0 ]; then
            error_message=""
            for i in "$@"; do
                error_message="$error_message$i"
            done
            print_warning "$error_message\n"
        fi

        # Exit with the same code as the last command
        exit $?
    fi
}

# Function that returns a message in red
function print_error {
    printf "\e[31m$1\e[0m\n"
}

# Function that returns a message in yellow
function print_warning {
    printf "\e[33m$1\e[0m\n"
}

# Function that returns a message in green
function print_success {
    printf "\e[32m$1\e[0m\n"
}

# Constants
# Default warning message
DEFAULT_WARNING_MESSAGE="=> This should not fail 😱, sorry 😢. Please investigate. Something is off.\n"
