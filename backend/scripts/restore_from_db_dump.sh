#!/bin/sh

# Set default values
DEFAULT_DB_NAME="postgres"
DEFAULT_DB_USER="digital-factory-local-user"
DEFAULT_DB_PASSWORD="digital-factory-local-password"
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DOCKER_CONTAINER="digital-factory-local-db"

# Parse named parameters
for ARG in "$@"; do
  case $ARG in
    -db_name=*)
      DB_NAME="${ARG#*=}"
      shift
      ;;
    -db_user=*)
      DB_USER="${ARG#*=}"
      shift
      ;;
    -db_password=*)
      DB_PASSWORD="${ARG#*=}"
      shift
      ;;
    -db_host=*)
      DB_HOST="${ARG#*=}"
      shift
      ;;
    -db_port=*)
      DB_PORT="${ARG#*=}"
      shift
      ;;
    -docker_container=*)
      DOCKER_CONTAINER="${ARG#*=}"
      shift
      ;;
    *)
      ;;
  esac
done

# Set default values if not provided
DB_NAME=${DB_NAME:-$DEFAULT_DB_NAME}
DB_USER=${DB_USER:-$DEFAULT_DB_USER}
DB_PASSWORD=${DB_PASSWORD:-$DEFAULT_DB_PASSWORD}
DB_HOST=${DB_HOST:-$DEFAULT_DB_HOST}
DB_PORT=${DB_PORT:-$DEFAULT_DB_PORT}
DOCKER_CONTAINER=${DOCKER_CONTAINER:-$DEFAULT_DOCKER_CONTAINER}

# Log information about specifying own arguments
echo "INFO: You can also overwrite default values like this: $0 -db_name=<database_name> -db_user=<db_user> -db_password=<db_password> -db_host=<db_host> -db_port=<db_port> -docker_container=<docker_container>"

# List available dump files
echo "Available dump files in the current directory:"
select DUMP_FILE in *.dump; do
    if [ -n "$DUMP_FILE" ]; then
        break
    else
        echo "Invalid selection. Please try again."
    fi
done

if [ ! -f "$DUMP_FILE" ]; then
    echo "The file '$DUMP_FILE' does not exist."
    exit 1
fi

echo "Restoring the database '$DB_NAME' from the dump file '$DUMP_FILE' using Docker container '$DOCKER_CONTAINER'..."

export PGPASSWORD=$DB_PASSWORD

# Copy the dump file to the Docker container
docker cp "$DUMP_FILE" "$DOCKER_CONTAINER:/tmp/$DUMP_FILE"

# Drop all tables in the database
docker exec -e PGPASSWORD=$DB_PASSWORD $DOCKER_CONTAINER psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"

# Restore the database using Docker
if docker exec -e PGPASSWORD=$DB_PASSWORD $DOCKER_CONTAINER pg_restore -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -v "/tmp/$DUMP_FILE"; then
  echo "Database '$DB_NAME' restored successfully from the dump file '$DUMP_FILE'."
else
  echo "Error restoring database '$DB_NAME' from the dump file '$DUMP_FILE'." >&2
fi