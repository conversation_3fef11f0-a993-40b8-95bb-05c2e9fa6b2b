#!/bin/sh

if [ "$#" -ne 5 ]; then
    echo "Usage: $0 <database_name> <db_user> <db_password> <db_host> <db_port>"
    exit 1
fi

DB_NAME=$1
DB_USER=$2
DB_PASSWORD=$3
DB_HOST=$4
DB_PORT=$5

echo "Destroying the database '$DB_NAME'..."

export PGPASSWORD=$DB_PASSWORD

# Terminate active connections to the target database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_activity 
WHERE pg_stat_activity.datname = '$DB_NAME' 
AND pid <> pg_backend_pid();"

psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS \"$DB_NAME\";"

echo "Database '$DB_NAME' destroyed successfully."
