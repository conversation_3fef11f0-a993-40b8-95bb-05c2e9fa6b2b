services:
  template-db:
    image: postgres:17.3
    restart: always
    environment:
      PGUSER: digital-factory-local-user
      POSTGRES_USER: digital-factory-local-user
      POSTGRES_PASSWORD: digital-factory-local-password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 1s
      timeout: 5s
      retries: 10
    ports:
      - 5432:5432

  template-mailpit:
    image: axllent/mailpit:v1.22
    restart: always
    ports:
      - 1025:1025
      - 8025:8025

volumes:
  pgdata: