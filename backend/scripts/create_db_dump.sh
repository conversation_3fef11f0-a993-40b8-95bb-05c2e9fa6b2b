#!/bin/sh

# Set default values
DEFAULT_DB_NAME="postgres"
DEFAULT_DB_USER="mzs-connector-local-user"
DEFAULT_DB_PASSWORD="mzs-connector-local-password"
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DUMP_FILE="mzs-connector_db_$(date +%Y-%m-%d_%H-%M-%S).dump"
DEFAULT_DOCKER_CONTAINER="mzs-connector-local-db"

# Parse named parameters
for ARG in "$@"; do
  case $ARG in
    -db_name=*)
      DB_NAME="${ARG#*=}"
      shift
      ;;
    -db_user=*)
      DB_USER="${ARG#*=}"
      shift
      ;;
    -db_password=*)
      DB_PASSWORD="${ARG#*=}"
      shift
      ;;
    -db_host=*)
      DB_HOST="${ARG#*=}"
      shift
      ;;
    -db_port=*)
      DB_PORT="${ARG#*=}"
      shift
      ;;
    -dump_file=*)
      DUMP_FILE="${ARG#*=}"
      shift
      ;;
    -docker_container=*)
      DOCKER_CONTAINER="${ARG#*=}"
      shift
      ;;
    *)
      ;;
  esac
done

# Set default values if not provided
DB_NAME=${DB_NAME:-$DEFAULT_DB_NAME}
DB_USER=${DB_USER:-$DEFAULT_DB_USER}
DB_PASSWORD=${DB_PASSWORD:-$DEFAULT_DB_PASSWORD}
DB_HOST=${DB_HOST:-$DEFAULT_DB_HOST}
DB_PORT=${DB_PORT:-$DEFAULT_DB_PORT}
DUMP_FILE=${DUMP_FILE:-$DEFAULT_DUMP_FILE}
DOCKER_CONTAINER=${DOCKER_CONTAINER:-$DEFAULT_DOCKER_CONTAINER}


echo "INFO: You can also  overwrite default values like this: $0 -db_name=<database_name> -db_user=<db_user> -db_password=<db_password> -db_host=<db_host> -db_port=<db_port> -dump_file=<dump_file> -docker_container=<docker_container>"

export PGPASSWORD=$DB_PASSWORD

# Create database dump using Docker
if docker exec -e PGPASSWORD=$DB_PASSWORD $DOCKER_CONTAINER pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -F c -b -v -f "/tmp/$DUMP_FILE"; then
  docker cp $DOCKER_CONTAINER:/tmp/$DUMP_FILE ./
  echo "Database dump successfully created in '$DUMP_FILE'."
else
  echo "Error creating database dump." >&2
fi