#!/bin/sh

# Set default values
DEFAULT_DB_NAME="postgres"
DEFAULT_DB_USER="mzs-connector-local-user"
DEFAULT_DB_PASSWORD="mzs-connector-local-password"
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DOCKER_CONTAINER="mzs-connector-local-db"

# Parse named parameters
for ARG in "$@"; do
  case $ARG in
    -db_name=*)
      DB_NAME="${ARG#*=}"
      shift
      ;;
    -db_user=*)
      DB_USER="${ARG#*=}"
      shift
      ;;
    -db_password=*)
      DB_PASSWORD="${ARG#*=}"
      shift
      ;;
    -db_host=*)
      DB_HOST="${ARG#*=}"
      shift
      ;;
    -db_port=*)
      DB_PORT="${ARG#*=}"
      shift
      ;;
    -docker_container=*)
      DOCKER_CONTAINER="${ARG#*=}"
      shift
      ;;
    *)
      ;;
  esac
done

# Set default values if not provided
DB_NAME=${DB_NAME:-$DEFAULT_DB_NAME}
DB_USER=${DB_USER:-$DEFAULT_DB_USER}
DB_PASSWORD=${DB_PASSWORD:-$DEFAULT_DB_PASSWORD}
DB_HOST=${DB_HOST:-$DEFAULT_DB_HOST}
DB_PORT=${DB_PORT:-$DEFAULT_DB_PORT}
DOCKER_CONTAINER=${DOCKER_CONTAINER:-$DEFAULT_DOCKER_CONTAINER}

# Log information about specifying own arguments
echo "INFO: You can also overwrite default values like this: $0 -db_name=<database_name> -db_user=<db_user> -db_password=<db_password> -db_host=<db_host> -db_port=<db_port> -docker_container=<docker_container>"

echo "Cleaning up the database '$DB_NAME' using Docker container '$DOCKER_CONTAINER'..."

export PGPASSWORD=$DB_PASSWORD

# Drop all tables in the database
docker exec -e PGPASSWORD=$DB_PASSWORD $DOCKER_CONTAINER psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"

if [ $? -eq 0 ]; then
    echo "Database '$DB_NAME' cleaned up successfully."
else
    echo "Failed to clean up the database '$DB_NAME'."
    exit 1
fi