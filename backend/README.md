# Backend - Spring Boot Application

A Spring Boot 3.3.4 application with Java 21, PostgreSQL database, and comprehensive DevOps integration.

## 🏗️ Architecture

### Technology Stack
- **Framework**: Spring Boot 3.3.4
- **Java Version**: 21
- **Database**: PostgreSQL (production), H2 (testing)
- **Migration**: Flyway
- **Build Tool**: Maven
- **Testing**: JUnit 5, Spring Boot Test

### Dependencies
- `spring-boot-starter-web` - REST API support
- `spring-boot-starter-data-jpa` - Database access
- `flyway-core` - Database migrations
- `postgresql` - PostgreSQL driver
- `lombok` - Code generation
- `h2` - In-memory database for testing

## 🚀 Getting Started

### Prerequisites
- Java 21
- Maven 3.6+
- PostgreSQL (for local development)

### Local Development Setup

1. **Start PostgreSQL database**
   ```bash
   # Using Docker (recommended)
   cd ../docker && ./start-db
   
   # Or use local PostgreSQL instance
   # Ensure database 'postgres' exists with user 'mzs-connector-local-user'
   ```

2. **Run the application**
   ```bash
   # With local profile
   ./mvnw spring-boot:run -Dspring-boot.run.profiles=local
   
   # With docker-stack profile
   ./mvnw spring-boot:run -Dspring-boot.run.profiles=docker-stack
   ```

3. **Access the application**
   - API Base URL: http://localhost:8080
   - Health Check: http://localhost:8080/actuator/health

## 🔧 Configuration

### Application Profiles

#### `application.properties` (Base)
- Application name and common settings

#### `application-local.properties`
- PostgreSQL connection to localhost:5432
- Local email server (Mailpit) configuration
- Development logging levels
- CRM service integration

#### `application-docker-stack.properties`
- PostgreSQL connection to Docker container
- Container-specific database settings
- Flyway migration configuration

### Test Configuration

#### `application.properties` (Test)
- H2 in-memory database
- Disabled Flyway migrations
- Test-specific logging

#### `application-pipeline.properties`
- CI/CD pipeline optimized settings
- H2 database for pipeline tests
- Minimal logging for performance

## 🗄️ Database

### Schema Management
- **Tool**: Flyway
- **Location**: `src/main/resources/db/migration/`
- **Naming**: `V{version}__{description}.sql`

### Database Scripts

Located in `scripts/` directory:

```bash
# Database management
./start-db              # Start PostgreSQL container
./clean_db.sh           # Clean database
./destroy_database.sh   # Remove database container
./rebuild-db            # Rebuild database from scratch

# Backup and restore
./create_db_dump.sh     # Create database backup
./restore_from_db_dump.sh # Restore from backup
./create_database_copy.sh # Create database copy
```

### Connection Details

**Local Development:**
- Host: localhost:5432
- Database: postgres
- Username: mzs-connector-local-user
- Password: mzs-connector-local-password

**Docker Stack:**
- Host: mzs-connector-db:5432
- Database: postgres
- Username: mzs-connector-local-user
- Password: mzs-connector-local-password

## 🧪 Testing

### Running Tests

```bash
# All tests
./mvnw test

# Specific test class
./mvnw test -Dtest=MZSConnectorApplicationBeApplicationTests

# With specific profile
./mvnw test -Dspring.profiles.active=test

# Skip tests
./mvnw package -DskipTests
```

### Test Configuration
- **Database**: H2 in-memory
- **Profile**: `test` (automatically activated)
- **Flyway**: Disabled for tests
- **DDL**: `create-drop` for isolation

### Test Structure
```
src/test/java/
└── com/example/mzsconnector/
    └── MZSConnectorApplicationBeApplicationTests.java

src/test/resources/
├── application.properties
├── application-test.properties
└── application-pipeline.properties
```

## 🏗️ Build & Deployment

### Maven Commands

```bash
# Clean and compile
./mvnw clean compile

# Run tests
./mvnw test

# Package application
./mvnw clean package

# Run application
./mvnw spring-boot:run

# Generate dependency tree
./mvnw dependency:tree
```

### Docker Build

```bash
# Development build
docker build -f Dockerfile.build -t mzs-connector-be:dev .

# Production build
docker build -f Dockerfile.prod -t mzs-connector-be:prod .

# Standard build
docker build -t mzs-connector-be .
```

## 📁 Project Structure

```
backend/
├── src/
│   ├── main/
│   │   ├── java/com/example/mzsconnector/
│   │   │   └── MZSConnectorApplication.java
│   │   └── resources/
│   │       ├── application*.properties
│   │       └── db/migration/
│   └── test/
│       ├── java/com/example/mzsconnector/
│       └── resources/
├── scripts/                 # Database management scripts
├── target/                  # Build output
├── Dockerfile*             # Container definitions
├── pom.xml                 # Maven configuration
└── README.md               # This file
```

## 🔍 Monitoring & Logging

### Application Logs
- **Location**: Console output (development)
- **Levels**: DEBUG (development), WARN (production)
- **Format**: Spring Boot default format

### Health Checks
- **Endpoint**: `/actuator/health`
- **Database**: Connection health included
- **Custom**: Add custom health indicators as needed

## 🚦 CI/CD Integration

### GitLab CI/CD
- **Build**: Maven compilation and packaging
- **Test**: Unit tests with H2 database
- **Quality**: SonarQube analysis
- **Security**: SAST scanning

### Pipeline Configuration
- **Profile**: `pipeline` for CI/CD runs
- **Database**: H2 in-memory for speed
- **Caching**: Maven dependencies cached

## 🛠️ Development Guidelines

### Code Style
- Follow Spring Boot conventions
- Use Lombok for boilerplate reduction
- Implement proper exception handling
- Add comprehensive tests

### Database Guidelines
- Use Flyway for all schema changes
- Version migrations properly
- Test migrations on development data
- Keep migrations idempotent

### Testing Guidelines
- Unit tests for business logic
- Integration tests for database operations
- Use `@SpringBootTest` for full context tests
- Mock external dependencies

## 🔧 Troubleshooting

### Common Issues

**Database Connection Failed**
```bash
# Check if database is running
cd ../docker && ./start-db

# Verify connection settings in application-*.properties
```

**Tests Failing**
```bash
# Ensure H2 dependency is present
# Check test profile configuration
# Verify Flyway is disabled for tests
```

**Build Failures**
```bash
# Clean and rebuild
./mvnw clean compile

# Check Java version
java -version  # Should be 21

# Verify Maven version
./mvnw -version
```

### Logs and Debugging
- Enable DEBUG logging: `logging.level.interzero.de=DEBUG`
- Database queries: `logging.level.org.hibernate.SQL=DEBUG`
- Web requests: `logging.level.org.springframework.web=DEBUG`

## 📚 Additional Resources

- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [Flyway Documentation](https://flywaydb.org/documentation/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Maven Documentation](https://maven.apache.org/guides/)