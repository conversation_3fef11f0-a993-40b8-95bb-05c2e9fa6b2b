# ----------------- G<PERSON><PERSON><PERSON>L CONFIGURATIONS ----------------- #
default:
  tags: ["aws"]

image: gitlab.interzero.de/software-development/dependency_proxy/containers/node:20.18.1-alpine

# Global variables
variables:
  COMPONENT: be
  COMPONENT_PATH: backend
  DEPLOYMENT_FILE_BACKEND: ${COMPONENT_PATH}/deployment.yml

# ----------------- TEMPLATES ----------------- #
.docker_build_template: &docker_build_template
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  script:
    - echo $DOCKER_AUTH_CONFIG > /kaniko/.docker/config.json
    - /kaniko/executor
      --context $CI_PROJECT_DIR/${COMPONENT_PATH}
      --dockerfile $CI_PROJECT_DIR/${COMPONENT_PATH}/Dockerfile${DOCKERFILE_EXTENSION}
      --build-arg PROJECT_NAME=$SHORT_PROJECT_NAME
      --build-arg COMPONENT=$COMPONENT
      --build-arg CI_COMMIT_REF_SLUG=$CI_COMMIT_REF_SLUG
      --insecure
      --insecure-pull
      --cache=true
      --cache-repo=nexus.interzero.de:${NEXUS_PORT}/${SHORT_PROJECT_NAME}-${COMPONENT_NAME}-cache
      --destination nexus.interzero.de:${NEXUS_PORT}/${SHORT_PROJECT_NAME}-${COMPONENT_NAME}:${CI_COMMIT_REF_SLUG}

.deployment_template: &deployment_template
  stage: deploy
  image: gitlab.interzero.de/software-development/dependency_proxy/containers/dtzar/helm-kubectl:3.16.4
  script:
    - echo "Deploy to ${DEPLOY_ENV} server"
    - echo "Delete old service"
    - kubectl delete -n ${NAMESPACE} deployment ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - echo "Add new service"
    - kubectl config view
    # Replace all placeholders in deployment file with environment variables
    - |
      export BRANCH="${CI_COMMIT_REF_SLUG}"
      export PROJECT_NAME="${SHORT_PROJECT_NAME}"
      # Use envsubst to replace all environment variables in the deployment file
      envsubst < ${DEPLOYMENT_FILE_BACKEND} > ${DEPLOYMENT_FILE_BACKEND}.tmp
      mv ${DEPLOYMENT_FILE_BACKEND}.tmp ${DEPLOYMENT_FILE_BACKEND}
    - cat ${DEPLOYMENT_FILE_BACKEND}
    - kubectl apply -f ${DEPLOYMENT_FILE_BACKEND}

be_set_feature_release_env:
  stage: deploy
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - echo $CI_COMMIT_REF_SLUG
    - MODIFIED_CI_COMMIT_REF_SLUG=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/--.*//')
    - echo "DYNAMIC_ENV_URL=https://${SHORT_PROJECT_NAME}-${COMPONENT}-${MODIFIED_CI_COMMIT_REF_SLUG}.feature.interzero.dev" >> deploy.env
  environment:
    name: review/${CI_COMMIT_REF_NAME}/backend
    url: $DYNAMIC_ENV_URL
    auto_stop_in: 21 days
    on_stop: be_destroy_feature_release
  artifacts:
    reports:
      dotenv: deploy.env

.deployment_template_feature_release: &deployment_template_feature_release
  <<: *deployment_template
# ----------------- DEPENDENCY INSTALLATION ----------------- #
be_install_dependencies:
  <<: *docker_build_template
  stage: install_dependencies
  tags: ["eks-entw-qat-with-s3"]
  only:
    - develop
    - staging
    - main
    - merge_requests
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}-build"
    DOCKERFILE_EXTENSION: ".build"

copy_database:
  stage: install_dependencies
  image: alpine:3.20.3
  tags: ["eks-entw-qat-with-s3"]
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - env
    - apk add --no-cache postgresql-client
    - echo "Creating a copy of the database for ${CI_COMMIT_REF_SLUG}"
    - ./backend/scripts/create_database_copy.sh ${PROJECT_NAME}-entw ${PROJECT_NAME}-${CI_COMMIT_REF_SLUG} ${DB_USER} ${DB_PASSWORD} ${DB_URL} 3000
  environment:
    name: review/${CI_COMMIT_REF_NAME}/database
    auto_stop_in: 21 days
    on_stop: delete_database

# ----------------- TESTING ----------------- #
be_unit_tests:
  stage: test
  image: nexus.interzero.de:5000/${SHORT_PROJECT_NAME}-${COMPONENT}-build:${CI_COMMIT_REF_SLUG}
  tags: ["eks-entw-qat-with-s3"]
  services:
    - name: postgres:17.3
      alias: postgres-db-test
      command: ["postgres", "-c", "max_connections=2500"]
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST_AUTH_METHOD: trust
  only:
    - develop
    - staging
    - main
    - merge_requests
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - cd /app
    - mvn test -Dspring.profiles.active=pipeline

# ----------------- DOCKER IMAGE BUILD ----------------- #
be_docker_build_develop:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - develop
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "dev"

be_docker_build_staging:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - staging
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "qat"

be_docker_build_main:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - main
  variables:
    NEXUS_PORT: "${NEXUS_PROD}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "prod"

be_docker_build_feature_release:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "dev"

# ----------------- DEPLOYMENT ----------------- #
be_deploy_develop:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-entw-qat"]
  only:
    - develop
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-oneepr"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT}.entw.interzero.dev"
    FRONTEND_HOSTNAME: "${SHORT_PROJECT_NAME}.entw.interzero.dev"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    SPRING_ENVIRONMENT: "dev"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    DB_SERVICE_NAME: "mzs-connector-entw"
    PREFIX: ""
  environment:
    name: ${CI_COMMIT_REF_NAME}/backend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT}.entw.interzero.dev"

be_deploy_staging:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-entw-qat"]
  only:
    - staging
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "qat-oneepr"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT}.qat.interzero.dev"
    FRONTEND_HOSTNAME: "${SHORT_PROJECT_NAME}.qat.interzero.dev"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    SPRING_ENVIRONMENT: "qat"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    DB_SERVICE_NAME: "mzs-connector-qat"
    PREFIX: ""
  environment:
    name: ${CI_COMMIT_REF_NAME}/backend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT}.qat.interzero.dev"

be_deploy_main:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-prod"]
  only:
    - main
  when: manual
  variables:
    DEPLOY_ENV: "PROD"
    NAMESPACE: "prod-oneepr"
    NEXUS_PORT: "${NEXUS_PROD}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT}.vpn.interzero.dev"
    FRONTEND_HOSTNAME: "${SHORT_PROJECT_NAME}.vpn.interzero.dev"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-prod-vpn"
    SPRING_ENVIRONMENT: "prod"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    DB_SERVICE_NAME: "mzs-connector-prod"
    PREFIX: ""
  environment:
    name: ${CI_COMMIT_REF_NAME}/backend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT}.vpn.interzero.dev"

be_deploy_feature_release:
  <<: *deployment_template_feature_release
  stage: deploy
  tags: ["eks-entw-qat"]
  before_script:
    - export SHORTEN_CI_COMMIT_REF_SLUG=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/--.*//')
    - export HOSTNAME="${SHORT_PROJECT_NAME}-${COMPONENT}-${SHORTEN_CI_COMMIT_REF_SLUG}.feature.interzero.dev"
    - export FRONTEND_HOSTNAME="${SHORT_PROJECT_NAME}-${SHORTEN_CI_COMMIT_REF_SLUG}.feature.interzero.dev"
    - export DB_SERVICE_NAME="mzs-connector-${CI_COMMIT_REF_SLUG}"
    - export PREFIX="${CI_COMMIT_REF_SLUG}-"
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-oneepr"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-feature-vpn"
    SPRING_ENVIRONMENT: "dev"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"

# ----------------- CLEANUP ----------------- #
be_destroy_feature_release:
  image: gitlab.interzero.de/software-development/dependency_proxy/containers/dtzar/helm-kubectl:3.16.4
  stage: cleanup
  when: manual
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  tags: ["eks-entw-qat"]
  script:
    - echo "Stopping environment ${CI_COMMIT_REF_SLUG}"
    - kubectl delete -n ${NAMESPACE} deployment ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} service ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} ingress ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} secret ${PREFIX}${SHORT_PROJECT_NAME}-secret --ignore-not-found
  variables:
    NAMESPACE: "entw-oneepr"
  environment:
    name: review/${CI_COMMIT_REF_NAME}/backend
    action: stop

delete_database:
  stage: cleanup
  when: manual
  image: alpine:3.20.3
  tags: ["eks-entw-qat"]
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - apk add --no-cache postgresql-client
    - echo "Creating a copy of the database for ${CI_COMMIT_REF_SLUG}"
    - ./backend/scripts/destroy_database.sh ${PROJECT_NAME}-${CI_COMMIT_REF_SLUG} ${DB_USER} ${DB_PASSWORD} ${DB_URL} 3000
  environment:
    name: review/${CI_COMMIT_REF_NAME}/database
    action: stop
