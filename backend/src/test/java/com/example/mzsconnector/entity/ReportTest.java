package com.example.mzsconnector.entity;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Report entity to verify proper construction and field mapping.
 */
@SpringBootTest
class ReportTest {

    @Test
    void testReportBuilder() {
        // Given
        LocalDate orderTime = LocalDate.of(2024, 11, 30);

        // When
        Report report = Report.builder()
                .orderId(1035023L)
                .orderNumber(54988L)
                .orderContractYear(2025)
                .orderContractStart(2025)
                .orderContractEnd(2025)
                .orderTime(orderTime)
                .orderArticleName("UMM")
                .userActive(1)
                .poStatId(1)
                .orderFractionGlas(BigDecimal.ZERO)
                .orderFractionPpk(BigDecimal.valueOf(10.000))
                .orderFractionWb(BigDecimal.ZERO)
                .orderFractionAlu(BigDecimal.ZERO)
                .orderFractionGvk(BigDecimal.ZERO)
                .orderFractionSv(BigDecimal.ZERO)
                .orderFractionKst(BigDecimal.ZERO)
                .orderFractionNat(BigDecimal.ZERO)
                .invoiceAmount(57.45)
                .relevantFlag(0)
                .build();
        
        // Then
        assertNotNull(report);
        assertEquals(1035023L, report.getOrderId());
        assertEquals(54988L, report.getOrderNumber());
        assertEquals(2025, report.getOrderContractYear());
        assertEquals(2025, report.getOrderContractStart());
        assertEquals(2025, report.getOrderContractEnd());
        assertEquals(orderTime, report.getOrderTime());
        assertEquals("UMM", report.getOrderArticleName());
        assertEquals(1, report.getUserActive());
        assertEquals(1, report.getPoStatId());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionGlas());
        assertEquals(BigDecimal.valueOf(10.000), report.getOrderFractionPpk());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionWb());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionAlu());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionGvk());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionSv());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionKst());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionNat());
        assertEquals(57.45, report.getInvoiceAmount());
        assertEquals(0, report.getRelevantFlag());
    }

    @Test
    void testReportNoArgsConstructor() {
        // When
        Report report = new Report();
        
        // Then
        assertNotNull(report);
        assertNull(report.getOrderId());
        assertNull(report.getOrderNumber());
    }

    @Test
    void testReportSettersAndGetters() {
        // Given
        Report report = new Report();
        LocalDate orderTime = LocalDate.of(2024, 11, 30);
        
        // When
        report.setOrderId(1035023L);
        report.setOrderNumber(54988L);
        report.setOrderContractYear(2025);
        report.setOrderContractStart(2025);
        report.setOrderContractEnd(2025);
        report.setOrderTime(orderTime);
        report.setOrderArticleName("UMM");
        report.setUserActive(1);
        report.setPoStatId(1);
        report.setOrderFractionGlas(BigDecimal.ZERO);
        report.setOrderFractionPpk(BigDecimal.valueOf(10.000));
        report.setOrderFractionWb(BigDecimal.ZERO);
        report.setOrderFractionAlu(BigDecimal.ZERO);
        report.setOrderFractionGvk(BigDecimal.ZERO);
        report.setOrderFractionSv(BigDecimal.ZERO);
        report.setOrderFractionKst(BigDecimal.ZERO);
        report.setOrderFractionNat(BigDecimal.ZERO);
        report.setInvoiceAmount(57.45);
        report.setRelevantFlag(0);
        
        // Then
        assertEquals(1035023L, report.getOrderId());
        assertEquals(54988L, report.getOrderNumber());
        assertEquals(2025, report.getOrderContractYear());
        assertEquals(2025, report.getOrderContractStart());
        assertEquals(2025, report.getOrderContractEnd());
        assertEquals(orderTime, report.getOrderTime());
        assertEquals("UMM", report.getOrderArticleName());
        assertEquals(1, report.getUserActive());
        assertEquals(1, report.getPoStatId());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionGlas());
        assertEquals(BigDecimal.valueOf(10.000), report.getOrderFractionPpk());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionWb());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionAlu());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionGvk());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionSv());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionKst());
        assertEquals(BigDecimal.ZERO, report.getOrderFractionNat());
        assertEquals(57.45, report.getInvoiceAmount());
        assertEquals(0, report.getRelevantFlag());
    }

    @Test
    void testReportArticleNameValidation() {
        // Given
        Report report = new Report();
        
        // When/Then - These should be valid values according to the specification
        report.setOrderArticleName("JPM"); // Jahres-Prognosemeldung
        assertEquals("JPM", report.getOrderArticleName());
        
        report.setOrderArticleName("UMM"); // Unterjährige Mengenmeldung
        assertEquals("UMM", report.getOrderArticleName());
        
        report.setOrderArticleName("JAM"); // Jahres-Abschlussmeldung
        assertEquals("JAM", report.getOrderArticleName());
    }
}
