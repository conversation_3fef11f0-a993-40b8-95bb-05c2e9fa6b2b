# Pipeline test configuration
spring.application.name=MZSConnectorApplication

# H2 Database configuration for pipeline tests
spring.datasource.url=jdbc:h2:mem:pipelinedb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate configuration for pipeline
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Flyway configuration for pipeline - disable for tests
spring.flyway.enabled=false

# Disable unnecessary features for pipeline tests
spring.jpa.open-in-view=false
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN