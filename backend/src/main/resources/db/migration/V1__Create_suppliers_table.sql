-- Create supplier table for MZS reporting
-- This table structure is hardcoded and should never be changed as per MZS requirements
-- Applies only to Germany and only to sales packaging

CREATE TABLE SUPPLIER (
    -- Primary key
    ID BIGSERIAL PRIMARY KEY,

    -- LUCID registration number - critical field requiring formal validation
    -- Structure: DE + 12 digits + 1 check digit (total 15 characters)
    -- Example: DE4076731467897
    REG_NR VARCHAR(120) NOT NULL,

    -- VAT ID of the supplier. If empty, TAX_NR must be set
    -- Example: DE257761637
    UMST_ID VARCHAR(120),

    -- VAT ID of the "Drittanbieter" (third party) in specific reporting model
    -- Can be empty for reports directly to IRA
    -- Example: DE257761637
    UMST_DRITT_ID VARCHAR(120),

    -- Tax number if there is one. If empty, UMST_ID must be set
    -- Example: *********
    TAX_NR VARCHAR(32),

    -- Either VAT ID or tax number. Cannot be blank - one must be set
    -- Example: DE257761637
    UMSTID_TAXNR VARCHAR(32) NOT NULL,

    -- Party number - irrelevant for OneEPR, should be left empty
    PARTEI_NR BIGINT,

    -- Customer number (supplier number, could be user ID)
    -- Example: 35861
    KUNDEN_NR VARCHAR(30) NOT NULL,

    -- OneEPR company name
    -- Example: MOSAIK Software
    KUNDEN_NAME VARCHAR(256) NOT NULL,

    -- Whether the supplier is active. Y/N flag
    -- Irrelevant for reporting quantities, used in PowerBI reports
    -- Example: Y
    ACTIVE_FLAG VARCHAR(1) NOT NULL CHECK (ACTIVE_FLAG IN ('Y', 'N')),

    -- Start of contract or service type association date
    -- Irrelevant for reporting, just for reference
    -- Example: 2025-08-21
    VALID_FROM_DT DATE NOT NULL,

    -- Indicates validity of LUCID number
    -- We check formally and put a 0 for valid numbers
    -- Example: 0
    VALID_FLAG INTEGER NOT NULL CHECK (VALID_FLAG >= 0 AND VALID_FLAG <= 999),

    -- Indicates whether the supplier is relevant for reporting
    -- 0 = relevant: reports will be submitted even if supplier is inactive
    -- 8 = irrelevant: reports will NOT be submitted even if supplier is active
    -- Example: 0
    RELEVANT_FLAG INTEGER NOT NULL CHECK (RELEVANT_FLAG IN (0, 8))
);


-- Add comments for documentation
COMMENT ON TABLE SUPPLIER IS 'supplier/Master data table for MZS reporting. Structure is hardcoded and should never be changed. Applies only to Germany and sales packaging.';
COMMENT ON COLUMN SUPPLIER.REG_NR IS 'LUCID registration number - critical field requiring formal validation using LUHN algorithm variant';
COMMENT ON COLUMN SUPPLIER.UMST_ID IS 'VAT ID of the supplier. If empty, TAX_NR must be set';
COMMENT ON COLUMN SUPPLIER.UMST_DRITT_ID IS 'VAT ID of Drittanbieter in specific reporting model';
COMMENT ON COLUMN SUPPLIER.TAX_NR IS 'Tax number if available. If empty, UMST_ID must be set';
COMMENT ON COLUMN SUPPLIER.UMSTID_TAXNR IS 'Either VAT ID or tax number - cannot be blank';
COMMENT ON COLUMN SUPPLIER.PARTEI_NR IS 'Party number - irrelevant for OneEPR';
COMMENT ON COLUMN SUPPLIER.KUNDEN_NR IS 'Customer/supplier number';
COMMENT ON COLUMN SUPPLIER.KUNDEN_NAME IS 'OneEPR company name';
COMMENT ON COLUMN SUPPLIER.ACTIVE_FLAG IS 'Whether supplier is active (Y/N)';
COMMENT ON COLUMN SUPPLIER.VALID_FROM_DT IS 'Start of contract or service association';
COMMENT ON COLUMN SUPPLIER.VALID_FLAG IS 'LUCID number validity indicator (0 = valid)';
COMMENT ON COLUMN SUPPLIER.RELEVANT_FLAG IS 'Reporting relevance (0 = relevant, 8 = irrelevant)';
