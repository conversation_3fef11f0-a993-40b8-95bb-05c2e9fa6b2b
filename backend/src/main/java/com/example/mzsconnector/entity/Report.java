package com.example.mzsconnector.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Entity representing actual quantity reports for MZS reporting.
 * This table contains all quantity report data submitted to MZS.
 * The structure is hardcoded and should never be changed as per MZS requirements.
 * Note: This applies only to Germany and only to sales packaging.
 * There can only be one report of each type for each year that is relevant.
 * If a supplier provides a second update during a year, all previous reports 
 * for the same year must be updated and set to irrelevant (RELEVANT_FLAG = 8).
 */
@Entity
@Table(name = "REPORT")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Report {

    /**
     * Quantity report ID. Primary key that must be mapped in OneEPR as the reported ID.
     * Could be the same as in OneEPR for simplicity if the format works.
     * Example: 1035023
     */
    @Id
    @Column(name = "ORDERID", nullable = false, unique = true)
    private Long orderId;

    /**
     * User's first name.
     * Example: Jeremias
     */
    @Column(name = "USERFIRSTNAME", nullable = false, length = 255)
    private String userFirstName;

    /**
     * User's last name.
     * Example: Loew
     */
    @Column(name = "USERLASTNAME", nullable = false, length = 255)
    private String userLastName;

    /**
     * Company name from OneEPR.
     * Example: Loewenbau
     */
    @Column(name = "COMPANY", nullable = false, length = 1020)
    private String company;

    /**
     * Contract VAT ID. If empty, CONTRACTTAXID must be set.
     * Example: DE350608869
     */
    @Column(name = "CONTRACTUSTID", length = 120)
    private String contractUstId;

    /**
     * Contract tax ID. If empty, CONTRACTUSTID must be set.
     */
    @Column(name = "CONTRACTTAXID", length = 120)
    private String contractTaxId;

    /**
     * Contract LUCID registration number.
     * Example: DE2060547948612
     */
    @Column(name = "CONTRACTREGNR", nullable = false, length = 120)
    private String contractRegNr;

    /**
     * The quantity report number, needed for reference.
     * Example: 54988
     */
    @Column(name = "ORDERNUMBER", nullable = false)
    private Long orderNumber;

    /**
     * Year of the quantity report.
     * Example: 2025
     */
    @Column(name = "ORDERCONTRACTYEAR", nullable = false)
    private Integer orderContractYear;

    /**
     * Year of first service association for packaging/Germany.
     * Example: 2025
     */
    @Column(name = "ORDERCONTRACTSTART", nullable = false)
    private Integer orderContractStart;

    /**
     * Year of contract end. Should be the current year unless the contract is extended.
     * Example: 2025
     */
    @Column(name = "ORDERCONTRACTEND", nullable = false)
    private Integer orderContractEnd;

    /**
     * Timestamp when the report is entered into the reporting table (created_at equivalent).
     * Example: 2024-11-30
     */
    @Column(name = "ORDERTIME", nullable = false)
    private LocalDate orderTime;

    /**
     * Type of report:
     * - JPM = Jahres-Prognosemeldung (Annual Forecast Report)
     * - UMM = Unterjährige Mengenmeldung (Interim Quantity Report)
     * - JAM = Jahres-Abschlussmeldung (Annual Final Report)
     * Example: UMM
     */
    @Column(name = "ORDERARTICLENAME", nullable = false, length = 255)
    private String orderArticleName;

    /**
     * Customer contract active flag.
     * 1 if customer contract active, 0 if inactive.
     * Example: 1
     */
    @Column(name = "USERACTIVE", nullable = false)
    private Integer userActive;

    /**
     * Status of the customer contract for the reporting year:
     * - 0 = inactive
     * - 1 = active
     * - 2 = in termination
     * - 3 = terminated
     * Example: 1
     */
    @Column(name = "PO_STAT_ID", nullable = false)
    private Integer poStatId;

    /**
     * Planned yearly quantities for glass in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONGLAS", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionGlas;

    /**
     * Planned yearly quantities for paper/carton in kg, maximum 3 decimals.
     * Example: 10.000
     */
    @Column(name = "ORDERFRACTIONPPK", nullable = false, precision = 26, scale = 3)
    private BigDecimal orderFractionPpk;

    /**
     * Planned yearly quantities for tinplate in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONWB", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionWb;

    /**
     * Planned yearly quantities for aluminium in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONALU", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionAlu;

    /**
     * Planned yearly quantities for carton drink packaging in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONGVK", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionGvk;

    /**
     * Planned yearly quantities for other packagings in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONSV", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionSv;

    /**
     * Planned yearly quantities for plastics in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONKST", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionKst;

    /**
     * Planned yearly quantities for natural materials in kg, maximum 3 decimals.
     * Example: 0.000
     */
    @Column(name = "ORDERFRACTIONNAT", nullable = false, precision = 22, scale = 3)
    private BigDecimal orderFractionNat;

    /**
     * Net total of the licensing invoice for the report.
     * Not relevant for reporting, used for PowerBI reports.
     * Example: 57.45
     */
    @Column(name = "INVOICEAMOUNT", nullable = false)
    private Double invoiceAmount;

    /**
     * Indicates whether the report is relevant or not:
     * - 0 = Yes (relevant)
     * - 8 = No (irrelevant)
     * There can only be one report of each type for each year that is relevant.
     * If the supplier gives a second update during a year, all previous reports
     * for the same year have to be updated and set to 8 here.
     * Example: 0
     */
    @Column(name = "RELEVANT_FLAG", nullable = false, precision = 3)
    private Integer relevantFlag;
}
