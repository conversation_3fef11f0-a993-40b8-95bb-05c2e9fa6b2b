package com.example.mzsconnector.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;

/**
 * Entity representing supplier/master data for MZS reporting.
 * This table contains all supplier information required for MZS quantity reporting.
 * The structure is hardcoded and should never be changed as per MZS requirements.
 * Note: This applies only to Germany and only to sales packaging.
 */
@Entity
@Table(name = "SUPPLIER")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Supplier {

    /**
     * Primary key for the supplier record.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    /**
     * LUCID registration number - critical field that needs formal validation.
     * Structure: DE + 12 digits + 1 check digit (total 15 characters).
     * Example: DE4076731467897
     * The LUCID Nr is validated using a variant of the LUHN algorithm with specific weighting factors.
     * Must be formally valid and results in VALID_FLAG being set to 0.
     */
    @Column(name = "REG_NR", nullable = false, length = 120)
    private String regNr;

    /**
     * VAT ID of the supplier. If empty, TAX_NR must be set.
     * Example: DE257761637
     */
    @Column(name = "UMST_ID", length = 120)
    private String umstId;

    /**
     * VAT ID of the "Drittanbieter" (third party) in the specific reporting model
     * called "Drittanbieter-Beauftragung". This will probably be the VAT ID of IEPR.
     * Can be empty for reports directly to IRA.
     * Example: DE257761637
     */
    @Column(name = "UMST_DRITT_ID", length = 120)
    private String umstDrittId;

    /**
     * Tax number if there is one. If empty, UMST_ID must be set.
     * Example: *********
     */
    @Column(name = "TAX_NR", length = 32)
    private String taxNr;

    /**
     * Either VAT ID or tax number. Cannot be blank - one of them must be set.
     * Example: DE257761637
     */
    @Column(name = "UMSTID_TAXNR", nullable = false, length = 32)
    private String umstidTaxnr;

    /**
     * Party number - irrelevant for OneEPR, should be left empty.
     */
    @Column(name = "PARTEI_NR")
    private Long parteiNr;

    /**
     * Customer number (supplier number, could be user ID).
     * Example: 35861
     */
    @Column(name = "KUNDEN_NR", nullable = false, length = 30)
    private String kundenNr;

    /**
     * OneEPR company name.
     * Example: MOSAIK Software
     */
    @Column(name = "KUNDEN_NAME", nullable = false, length = 256)
    private String kundenName;

    /**
     * Whether the supplier is active. Y/N flag.
     * Irrelevant for reporting quantities, used in PowerBI reports to aggregate users.
     * Example: Y
     */
    @Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
    private String activeFlag;

    /**
     * Start of contract or service type association date.
     * Irrelevant for reporting, just for reference.
     * Example: 2025-08-21
     */
    @Column(name = "VALID_FROM_DT", nullable = false)
    private LocalDate validFromDt;

    /**
     * Indicates validity of LUCID number.
     * We check formally and put a 0 for valid numbers.
     * Example: 0
     */
    @Column(name = "VALID_FLAG", nullable = false, precision = 3)
    private Integer validFlag;

    /**
     * Indicates whether the supplier is relevant for reporting.
     * - 0 = relevant: reports will be submitted even if supplier is inactive
     * - 8 = irrelevant: reports will NOT be submitted even if supplier is active,
     *       and all previous submitted reports for this year will be retracted
     * Example: 0
     */
    @Column(name = "RELEVANT_FLAG", nullable = false, precision = 3)
    private Integer relevantFlag;
}
