/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { PasswordInput } from "@/components/ui/password-input";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { usePathname, Link } from "@/i18n/navigation";
import { enqueueSnackbar, useSnackbar } from "notistack";
import { useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { SuccessRecoverPassword } from "./success-recover-password";
import { AuthTitle } from "../components/auth-title";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";
import { z } from "zod";
import { recoverPassword } from "@/lib/api/auth";

const recoverPasswordSchema = z.object({
  password: z.string().min(6),
});

type RecoverPasswordFormData = z.infer<typeof recoverPasswordSchema>;

export function RecoverPasswordPage() {
  const [recover, setRecover] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<RecoverPasswordFormData>({
    resolver: zodResolver(recoverPasswordSchema),
  });

  async function submit({ password }: RecoverPasswordFormData) {
    setIsLoading(true);

    if (!token) return;

    const response = await recoverPassword(token, password, "PASSWORD_RESET");

    if (response.status === 500) {
      enqueueSnackbar(response.data, { variant: "error" });
      return setIsLoading(false);
    }

    if (response.status != 201) {
      enqueueSnackbar(response.data?.message, { variant: "error" });
      return setIsLoading(false);
    }

    setRecover(true);
    setIsLoading(false);
  }

  const { paramValues } = useQueryFilter(["token", "email"]);

  const token = paramValues.token;
  const email = paramValues.email;

  const loginUrl = pathname.includes("partner-hub") ? "/partner-hub/auth/login" : "/auth/login";

  const t = useTranslations("RecoverPassword");

  const password = useWatch({ control: control, name: "password" });

  if (recover) return <SuccessRecoverPassword />;

  return (
    <>
      <AuthTitle title={t("title")} subtitle={t("subtitle")} />
      <form onSubmit={handleSubmit(submit)}>
        <div className="flex mb-10 ">
          <div className="w-full  mb-5">
            <div className="flex mb-3">
              <PasswordInput
                label={t("password.label")}
                placeholder={t("password.placeholder")}
                {...register("password", {})}
                variant={errors.password ? "error" : "enabled"}
                errorMessage={errors.password && errors.password.message}
                errorIcon={errors.password && <Error className="fill-error size-5" />}
              />
            </div>
            <div className="text-tonal-dark-cream-30 font-light text-sm">
              <PasswordStrengthBar password={password} />
            </div>
          </div>
        </div>

        <Button
          color="yellow"
          size="medium"
          variant="filled"
          disabled={isLoading || !!errors.password}
          style={{ width: "100%" }}
        >
          {isLoading ? "Loading..." : t("button")}
        </Button>
      </form>
      <div className="mt-4">
        <div className="flex justify-center">
          <Link href={loginUrl}>
            <span className="text-sm cursor-pointer font-medium text-tonal-blue-40 hover:underline">{t("back")}</span>
          </Link>
        </div>
      </div>
    </>
  );
}
