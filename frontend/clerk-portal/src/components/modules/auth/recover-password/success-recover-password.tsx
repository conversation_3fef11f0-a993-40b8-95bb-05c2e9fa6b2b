"use client";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { Link } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useTranslations } from "next-intl";
import { AuthTitle } from "../components/auth-title";

export function SuccessRecoverPassword() {
  const { paramValues } = useQueryFilter(["email"]);
  const email = paramValues.email;

  const t = useTranslations("RecoverPassword");

  return (
    <>
      <AuthTitle title={t("confirmation.title")} />
      <div className="text-primary flex items-center gap-2 mt-4">
        <p>{t("confirmation.subtitle")}</p>
        <p className="font-bold">{email}</p>
      </div>
      <div className="flex justify-center mt-10 w-full">
        <Link href="/auth/login" className="w-full">
          <Button className="w-full" size="medium" variant="filled" color="dark-blue">
            {t("confirmation.button")}
          </Button>
        </Link>
      </div>
    </>
  );
}
