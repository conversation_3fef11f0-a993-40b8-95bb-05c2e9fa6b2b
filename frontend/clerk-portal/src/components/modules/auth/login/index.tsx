"use client";

import { PasswordInput } from "@/components/ui/password-input";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { Link } from "@/i18n/navigation";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { AuthTitle } from "../components/auth-title";

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginPage() {
  const [loading, setLoading] = useState(false);

  const { paramValues } = useQueryFilter(["redirect", "email"]);

  const redirect = paramValues.redirect;

  const {
    register,
    handleSubmit,
    setError,
    getValues,
    formState: { errors, isValid },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: "onBlur",
    defaultValues: {
      email: paramValues.email || undefined,
    },
  });

  const t = useTranslations("Login");
  const c = useTranslations("common");

  async function submit(data: LoginFormData) {
    setLoading(true);

    try {
      const response = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: true,
        intent: UserTypes.CLERK,
        callbackUrl: redirect || "/",
      });

      if (response?.error === "User not found") {
        setLoading(false);
        setError("email", { message: "Email not found" });
        return;
      }

      if (response?.error) {
        setLoading(false);
        setError("email", { message: t("invalidEmail") });
        setError("password", { message: t("invalidPassword") });
        return;
      }
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <>
      <AuthTitle title={t("credentials")} subtitle={t("credentials")} />
      <form className="w-full" onSubmit={handleSubmit(submit)}>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-5">
            <div className="flex">
              <Input
                label={t("email")}
                placeholder={t("emailPlaceholder")}
                type="email"
                variant={errors.email ? "error" : "enabled"}
                {...register("email", { required: true })}
                rightIcon={
                  errors.email ? (
                    <Error className="fill-error size-5" />
                  ) : loginSchema.shape.email.safeParse(getValues("email")).success ? (
                    getValues("email") && <Check className="fill-tonal-green-40 size-5" />
                  ) : null
                }
                errorMessage={errors.email && errors.email.message}
              />
            </div>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-12">
            <div className="flex">
              <PasswordInput
                label={t("password")}
                placeholder={t("passwordPlaceholder")}
                {...register("password", { required: true })}
                variant={errors.password ? "error" : "enabled"}
                errorMessage={errors.password && errors.password.message}
                errorIcon={errors.password && <Error className="fill-error size-5" />}
              />
            </div>
            <div className="mt-2">
              <Link href="/auth/forgot-password" className="inline-block">
                <Button type="button" variant="text" size="small" color="light-blue">
                  {t("forgotPassword")}
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-3">
            <Button variant="filled" size="medium" color="yellow" disabled={!isValid || loading} className="w-full">
              {loading ? c("loading") : c("continue")}
            </Button>
          </div>
        </div>
      </form>
    </>
  );
}
