"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useTranslations } from "next-intl";
import { usePathname, Link } from "@/i18n/navigation";
import { AuthTitle } from "../components/auth-title";

export function SuccessResetPassword() {
  const pathname = usePathname();

  const c = useTranslations("common");
  const t = useTranslations("ForgotPassword");

  const loginUrl = pathname.includes("partner-hub") ? "/partner-hub/auth/login" : "/auth/login";

  return (
    <>
      <AuthTitle title={c("thankYou")} subtitle={t("instructions")} />
      <div className="flex justify-center mt-10 w-full">
        <Link href={loginUrl} className="w-full">
          <Button className="w-full" size="medium" variant="filled" color="dark-blue">
            {t("button")}
          </Button>
        </Link>
      </div>
    </>
  );
}
