"use clinet";

import { useState } from "react";

import type { NumberFormatValues, NumericFormatProps, SourceInfo } from "react-number-format";
import { NumericFormat } from "react-number-format";

import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";

interface CurrencyInputProps extends NumericFormatProps {
  label?: string;
  hideRightIcon?: boolean;
}

export function CurrencyInput(props: CurrencyInputProps) {
  const { onValueChange, placeholder = "€ 0,00", label, hideRightIcon, ...restProps } = props;
  const [isValidValue, setIsValidValue] = useState(false);

  function handleValueChange(values: NumberFormatValues, sourceInfo: SourceInfo) {
    if (values.floatValue) {
      setIsValidValue(true);
      onValueChange?.(values, sourceInfo);
    } else {
      setIsValidValue(false);
    }
  }

  return (
    <div className="relative">
      <NumericFormat
        {...restProps}
        customInput={Input}
        label={label}
        // thousandSeparator="."
        // decimalSeparator=","
        prefix="€ "
        allowNegative={true}
        decimalScale={2}
        fixedDecimalScale={true}
        displayType="input"
        placeholder={placeholder}
        onValueChange={handleValueChange}
        // className='[&_input]:!pr-40'
      />
      {isValidValue && !hideRightIcon && (
        <div className="absolute top-12 right-0 pr-4">
          <CheckCircle className="size-5 fill-primary" />
        </div>
      )}
    </div>
  );
}
