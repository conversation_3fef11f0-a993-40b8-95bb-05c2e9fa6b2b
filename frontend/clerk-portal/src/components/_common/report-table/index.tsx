import { ReactNode } from "react";
import { FractionIcon } from "../fraction-icon";

import { ReportSetColumn, ReportSetFraction } from "@/lib/api/report-set/types";
import { FractionInput } from "@/components/ui/fraction-input";

interface ReportTableProps {
  fractions: ReportSetFraction[];
  columns: ReportSetColumn[];
  field?: (
    field: { disabled: boolean },
    fraction: { fractionId: number; fractionCode: string; columnId: number; columnCode: string }
  ) => ReactNode;
}

export function ReportTable({ fractions, columns, field }: ReportTableProps) {
  return (
    <div className="rounded-[20px] overflow-hidden space-y-[1px]">
      <div className="w-full text-primary text-sm bg-background">
        <div className="flex items-stretch text-sm font-normal gap-[1px]">
          <div className="py-6 px-2 bg-surface-03 w-44"></div>
          {columns.map((column) => (
            <div key={column.code} className="flex-1 py-4 px-2 bg-surface-03">
              {column.name}
            </div>
          ))}
        </div>
        <div className="flex flex-col gap-[1px]">
          {fractions.map((firstLevelFraction) => (
            <>
              <div className="flex items-stretch w-full gap-[1px]">
                <div className="bg-[#A9C8FF] p-2 w-44 text-base overflow-hidden text-ellipsis whitespace-nowrap">
                  {firstLevelFraction.name}
                </div>
                {columns.map((column) => (
                  <div key={column.code} className="bg-[#A9C8FF] p-2 flex-1"></div>
                ))}
              </div>
              {!firstLevelFraction.has_second_level && (
                <>
                  <ReportTableSecondColumns columns={columns} />
                  <ReportTableDeclareRow fraction={firstLevelFraction} columns={columns} field={field} />
                </>
              )}
              {firstLevelFraction.has_second_level && (
                <>
                  {firstLevelFraction.children.map((secondLevelFraction) => (
                    <>
                      <ReportTableSecondColumns
                        columns={columns}
                        secondLevelFractionName={
                          firstLevelFraction.has_third_level ? secondLevelFraction.name : undefined
                        }
                      />
                      {firstLevelFraction.has_third_level &&
                        secondLevelFraction.children.map((thirdLevelFraction) => (
                          <ReportTableDeclareRow
                            key={thirdLevelFraction.code}
                            fraction={thirdLevelFraction}
                            columns={columns}
                            field={field}
                          />
                        ))}
                      {!firstLevelFraction.has_third_level && (
                        <ReportTableDeclareRow fraction={secondLevelFraction} columns={columns} field={field} />
                      )}
                    </>
                  ))}
                </>
              )}
            </>
          ))}
        </div>
      </div>
    </div>
  );
}

interface ReportTableDeclareRowProps {
  fraction: ReportSetFraction;
  columns: ReportTableProps["columns"];
  field?: (
    field: { disabled: boolean },
    fraction: { fractionId: number; fractionCode: string; columnId: number; columnCode: string }
  ) => ReactNode;
}

function ReportTableDeclareRow({ fraction, columns, field }: ReportTableDeclareRowProps) {
  return (
    <div className="flex items-stretch w-full gap-[1px]">
      <div className="bg-surface-03 p-2 w-44 flex items-center gap-2">
        <span className="w-3 h-3 bg-[#808FA9] rounded-full"></span>
        <FractionIcon size="small" iconUrl={fraction.fraction_icon.image_url} />
        {fraction.name}
      </div>
      {columns.map((column) => (
        <div key={column.code} className="flex-1 flex items-stretch gap-[1px]">
          {column.children ? (
            column.children.map((secondLevelColumn) => (
              <div key={secondLevelColumn.code} className="flex-1 p-2">
                {field ? (
                  field(
                    {
                      disabled: !secondLevelColumn.fractions.find(
                        (columnFraction) => columnFraction.fraction_code === fraction.code
                      ),
                    },
                    {
                      fractionId: fraction.id,
                      fractionCode: fraction.code,
                      columnId: secondLevelColumn.id,
                      columnCode: secondLevelColumn.code,
                    }
                  )
                ) : (
                  <FractionInput type="currency" className="h-10 rounded-2xl" />
                )}
              </div>
            ))
          ) : (
            <div key={column.code} className="flex-1 p-2">
              {field ? (
                field(
                  { disabled: false },
                  { fractionId: fraction.id, fractionCode: fraction.code, columnId: column.id, columnCode: column.code }
                )
              ) : (
                <FractionInput type="currency" className="h-10 rounded-2xl" />
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

interface ReportTableSecondColumnsProps {
  columns: ReportTableProps["columns"];
  secondLevelFractionName?: string;
}

function ReportTableSecondColumns({ columns, secondLevelFractionName }: ReportTableSecondColumnsProps) {
  return (
    <div className="flex items-stretch gap-[1px]">
      <div className="bg-[#CCE4FF] p-2 w-44 text-base overflow-hidden text-ellipsis whitespace-nowrap">
        {secondLevelFractionName}
      </div>
      {columns.map((column) => (
        <div key={column.code} className="flex-1 flex items-stretch gap-[1px]">
          {column.children ? (
            column.children.map((secondLevelColumn) => (
              <div key={secondLevelColumn.code} className="flex-1 bg-[#CCE4FF] p-2">
                {secondLevelColumn.name}
              </div>
            ))
          ) : (
            <div key={column.code} className="flex-1 bg-[#CCE4FF] p-2">
              {column.name}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
