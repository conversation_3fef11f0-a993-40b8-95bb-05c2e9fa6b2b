import React, { FC, InputHTMLAttributes } from "react";

interface CustomCheckboxProps extends InputHTMLAttributes<HTMLInputElement> {
  id: string;
  defaultChecked?: boolean;
}

const CustomCheckbox: FC<CustomCheckboxProps> = ({ id, defaultChecked = false, ...props }) => {
  return (
    <div className="inline-flex items-center">
      <label htmlFor={id} className="flex items-center cursor-pointer relative">
        <input
          defaultChecked={defaultChecked}
          type="checkbox"
          id={id}
          className="peer h-6 w-6 cursor-pointer transition-all appearance-none rounded-full bg-slate-100 shadow hover:shadow-md border-[2px] border-tonal-dark-cream-80 checked:border-primary"
          {...props}
        />
        <span className="absolute pointer-events-none text-white opacity-0 peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-3 h-3 bg-primary rounded-full"></div>
        </span>
      </label>
    </div>
  );
};

export default CustomCheckbox;
