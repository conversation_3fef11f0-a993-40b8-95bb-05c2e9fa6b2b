"use client";

import { ChangeEvent, useRef, useState } from "react";
import { Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CountryIcon } from "../../country-icon";
import { cn } from "@/lib/utils";
import { PublishedCountry } from "@/hooks/use-liberated-countries";
import { useTranslations } from "next-intl";

interface CountryInputProps {
  countries: PublishedCountry[];
  onSelectCountry?: (country: PublishedCountry) => void;
  label?: string;
  className?: string;
}

export function CountryInput({ countries, onSelectCountry, label = "Country name", className }: CountryInputProps) {
  const t = useTranslations("CustomerProfileHeader");
  const [search, setSearch] = useState("");

  const inputRef = useRef<HTMLInputElement | null>(null);

  const searchCountries =
    search !== ""
      ? countries.filter((c) => c.name.toLocaleLowerCase().includes(search.toLowerCase().trim()))
      : countries;

  return (
    <div className={cn("relative", className)}>
      <Popover>
        <PopoverTrigger className="[&>div]:items-start w-full" autoFocus={false}>
          <Input
            ref={inputRef}
            leftIcon={<Search width={24} height={24} className="fill-tonal-dark-cream-60" />}
            label={label}
            placeholder={t("searchCountry")}
            value={search}
            onChange={(e: ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
            LabelClasses={`font-bold`}
          />
        </PopoverTrigger>
        <PopoverContent
          className="shadow-elevation-04-1 pointer-events-auto p-0 py-3 rounded-2xl"
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          {!!searchCountries.length &&
            searchCountries.map((country) => (
              <button
                key={country.name}
                className="flex items-center py-5 px-4 gap-4 hover:bg-surface-02 cursor-pointer w-full"
                onClick={() => {
                  !!onSelectCountry && onSelectCountry(country);

                  setSearch("");

                  inputRef.current?.focus();
                }}
                type="button"
              >
                <CountryIcon country={country} className="size-6" />
                <p className="text-primary">{country.name}</p>
              </button>
            ))}
        </PopoverContent>
      </Popover>
    </div>
  );
}
