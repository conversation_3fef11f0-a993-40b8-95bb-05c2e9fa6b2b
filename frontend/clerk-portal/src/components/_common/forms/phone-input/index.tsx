"use client";

import { ChangeEvent, InputHTMLAttributes, MutableRefObject, useCallback, useEffect, useRef, useState } from "react";
import { BiChevronDown } from "react-icons/bi";
import InputMask from "react-input-mask";

import {
  Combobox,
  ComboboxContent,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxList,
  ComboboxSearch,
  ComboboxTrigger,
} from "@/components/ui/combobox/refactor";
import { cn } from "@/lib/utils";

import { CountryIcon } from "@/components/_common/country-icon";
import { COUNTRIES } from "@/utils/countries";
import { CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { getPhoneMask } from "./getPhoneMask";
import { useTranslations } from "next-intl";
import { validatePhoneNumber } from "@/utils/validate-phone-number";

const GERMANY = COUNTRIES.find((c) => c.code === "DE")!;

type Country = (typeof COUNTRIES)[number];

interface PhoneInputProps extends InputHTMLAttributes<HTMLInputElement> {
  defaultValue?: string;
  valueSetter: (value?: string) => void;
  errorSetter: (valid: boolean) => void;
  isError?: boolean;
  required?: boolean;
  isMenuDiv?: boolean;
}

const getCountry = (defaultValue?: string) => {
  if (!defaultValue) return GERMANY;

  const country = COUNTRIES.find((c) => defaultValue.startsWith(c.phone.code));

  if (!country) return GERMANY;

  return country;
};

export function PhoneInput({
  defaultValue,
  placeholder,
  valueSetter,
  errorSetter,
  isError,
  required = true,
  isMenuDiv = false,
  ...props
}: PhoneInputProps) {
  const phoneInputRef = useRef<HTMLInputElement | null>(null);
  const prevValue = useRef<string | null>(null);

  const [isInvalid, setIsInvalid] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country>(getCountry(defaultValue));

  const phoneWithoutCode = defaultValue ? defaultValue.replace(selectedCountry.phone.code, "") : "";

  const [dataMask, setDataMask] = useState(getPhoneMask(selectedCountry || GERMANY, phoneWithoutCode || ""));
  const [codeCountry, setCodeCountry] = useState(selectedCountry.phone.code || GERMANY.phone.code);
  const [menuOpen, setMenuOpen] = useState(false);

  const validatePhoneInput = useCallback(
    (phone: string, mask?: string) => {
      const valid = validatePhoneNumber(phone, mask || dataMask.mask);
      return valid;
    },
    [dataMask.mask]
  );

  const handleError = (valid = !isInvalid) => {
    if (phoneInputRef?.current?.value || required) {
      errorSetter && errorSetter(valid);
    } else if (!required) {
      errorSetter && errorSetter(true);
    }
  };

  function handleSelectCountry(countryName: string) {
    const country = COUNTRIES.find((c) => c.name === countryName);

    if (!country) return;

    setSelectedCountry(country);
    setCodeCountry(country.phone.code);

    const newMask = getPhoneMask(country, phoneInputRef?.current?.value || "");
    setDataMask(newMask);

    valueSetter(`${country.phone.code}`);

    if (phoneInputRef.current && phoneInputRef.current.value) {
      const valid = validatePhoneInput(phoneInputRef.current.value, newMask.mask);

      setIsInvalid(!valid);
      handleError(valid);
    }
  }

  function handleOnChangeCodeCountry(event: ChangeEvent<HTMLInputElement>) {
    const value = event.target.value;

    setCodeCountry(value);
    const country = COUNTRIES.find((country) => country.phone.code === value);

    if (country) {
      handleSelectCountry(country.name);
    }
  }

  function handleOnBlurCodeCountry() {
    setCodeCountry(selectedCountry.phone.code);
    handleError(!isInvalid);
  }

  const handleOnBlur = () => {
    handleError();
  };

  const handleOpenSelect = () => {
    setMenuOpen((current) => !current);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setMenuOpen(false);
    }, 200);
  };

  useEffect(() => {
    const valid = validatePhoneInput(phoneWithoutCode);
    setIsInvalid(!valid);
  }, [phoneWithoutCode, validatePhoneInput]);

  useEffect(() => {
    if (phoneInputRef.current) {
      (phoneInputRef.current as any).setCursorToEnd();
    }
  }, [dataMask]);

  useEffect(() => {
    const newCountrySelect = getCountry(defaultValue);
    setSelectedCountry(newCountrySelect);

    const phoneWithoutCode = defaultValue ? defaultValue.replace(newCountrySelect.phone.code, "") : "";

    const newMask = getPhoneMask(newCountrySelect || GERMANY, phoneWithoutCode || "");

    setDataMask(newMask);
    setCodeCountry(newCountrySelect.phone.code || GERMANY.phone.code);

    const valid = validatePhoneInput(phoneWithoutCode, newMask?.mask);
    setIsInvalid(!valid);
  }, [defaultValue, validatePhoneInput]);

  const c = useTranslations("common");
  const t = useTranslations("PhoneInput");
  return (
    <div
      data-error={isError}
      data-disabled={props.disabled}
      onBlur={handleBlur}
      className="flex items-center bg-background w-full rounded-2xl p-4 border border-tonal-dark-cream-80 text-tonal-dark-cream-60 data-[selected=true]:text-tonal-dark-cream-10 aria-expanded:outline-primary data-[error=true]:border-error data-[error=true]:bg-tonal-red-90 data-[disabled=true]:bg-tonal-dark-cream-80"
    >
      <div className="flex gap-1 h-6 relative">
        {isMenuDiv ? (
          <div>
            <button className="flex items-center" onClick={handleOpenSelect} type="button">
              <CountryIcon
                country={selectedCountry}
                data-disabled={props.disabled}
                className="mr-1 data-[disabled=true]:grayscale"
              />
              <BiChevronDown className={cn("h-5 w-5", props.disabled ? "fill-tonal-dark-cream-80" : "fill-primary")} />
            </button>
            {menuOpen && (
              <div className="w-[250px] max-h-40 overflow-auto bg-white absolute top-12 rounded-2xl shadow-elevation-04-1 z-50 left--10">
                <div className="w-full p-0 shadow-elevation-04-1">
                  <div className="overflow-hidden text-foreground">
                    <div className="flex flex-col gap-1">
                      {COUNTRIES.map((country) => (
                        <button
                          key={country.code}
                          value={country.name}
                          onClick={() => handleSelectCountry(country.name)}
                          className="text-primary flex items-center gap-5 hover:bg-secondary p-2"
                          type="button"
                        >
                          <CountryIcon country={country} className="size-6 mr-2" />
                          {country.name}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <Combobox>
            <ComboboxTrigger disabled={props.disabled} asChild>
              <button className="flex items-center">
                <CountryIcon
                  country={selectedCountry}
                  data-disabled={props.disabled}
                  className="mr-1 data-[disabled=true]:grayscale"
                />
                <BiChevronDown
                  className={cn("h-5 w-5", props.disabled ? "fill-tonal-dark-cream-80" : "fill-primary")}
                />
              </button>
            </ComboboxTrigger>
            <ComboboxContent
              alignOffset={-16}
              sideOffset={24}
              align="start"
              className="w-[250px] p-0 shadow-elevation-04-1"
            >
              <ComboboxSearch className="text-primary" placeholder={t("searchCountry")} />
              <ComboboxList>
                <ComboboxEmpty>{t("noCountry")}</ComboboxEmpty>
                <ComboboxGroup>
                  {COUNTRIES.map((country) => (
                    <ComboboxItem
                      key={country.code}
                      value={country.name}
                      onSelect={handleSelectCountry}
                      className="hover:bg-secondary text-primary"
                    >
                      <CountryIcon country={country} className="size-6 mr-2" />
                      {country.name}
                    </ComboboxItem>
                  ))}
                </ComboboxGroup>
              </ComboboxList>
            </ComboboxContent>
          </Combobox>
        )}
        <InputMask
          mask="+9999"
          maskChar=""
          onBlur={handleOnBlurCodeCountry}
          className={cn(
            "h-full w-9 outline-0 border-0 flex-1 text-right text-base bg-[transparent]",
            props.disabled ? "text-on-surface-01" : "text-primary"
          )}
          value={codeCountry}
          onChange={handleOnChangeCodeCountry}
          {...props}
        />
        <div className="h-full w-[1px] bg-tonal-dark-cream-80 ml-1 text-right"></div>
      </div>
      <div className="flex-1 h-6 pl-3">
        <InputMask
          ref={phoneInputRef as MutableRefObject<InputMask | null>}
          maskChar=""
          mask={
            Array.isArray(selectedCountry.phone.mask)
              ? dataMask.mask + (!dataMask.isBiggestMask ? "9" : "")
              : selectedCountry.phone.mask
          }
          onChange={(event) => {
            const { value: phone } = event.target;
            const newMask = getPhoneMask(selectedCountry, phone);
            setDataMask(newMask);

            const valid = validatePhoneInput(phone, newMask.mask);
            setIsInvalid(!valid);

            if (phone === prevValue.current) return;

            valueSetter(
              phone ? `${selectedCountry.phone.code}${phone}` : `${selectedCountry?.phone?.code}` || undefined
            );

            prevValue.current = phone;
          }}
          className={cn(
            "h-full w-full outline-0 border-0 flex-1 bg-[transparent]",
            props.disabled ? "text-on-surface-01" : "text-primary"
          )}
          placeholder={
            placeholder || Array.isArray(selectedCountry.phone.mask) ? dataMask.mask : selectedCountry.phone.mask
          }
          aria-invalid={isInvalid}
          value={phoneWithoutCode}
          onBlur={handleOnBlur}
          {...props}
        />
      </div>
      <CheckCircle
        className={cn(
          "fill-success transition-all h-5",
          isInvalid ? "opacity-0 w-0" : "opacity-100 w-5 ml-3",
          props.disabled && "hidden"
        )}
      />
    </div>
  );
}
