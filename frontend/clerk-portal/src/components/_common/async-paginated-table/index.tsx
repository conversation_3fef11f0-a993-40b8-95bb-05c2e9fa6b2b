import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

interface IAsyncPaginatedTable<T extends object> {
  data: T[];
  columns: ColumnDef<T, any>[];
  currentPage: number;
  pages: number;
  pageSize?: number;
  isLoading?: boolean;
  onPageChange: (page: number) => void;
  noResultsMessage: string;
  showHeaderOnNoResults?: boolean;
}

export default function AsyncPaginatedTable<T extends object>({
  columns,
  data,
  currentPage,
  pages,
  pageSize = 10,
  isLoading = false,
  onPageChange,
  noResultsMessage,
  showHeaderOnNoResults = false,
}: IAsyncPaginatedTable<T>) {
  const [totalPages, setTotalPages] = useState(pages);

  useEffect(() => {
    if (!isLoading && pages > 0) {
      setTotalPages(pages);
    }
  }, [pages, isLoading]);

  const table = useReactTable<T>({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize: data.length,
      },
    },
  });

  if (!isLoading && data.length === 0) {
    if (!showHeaderOnNoResults) {
      return (
        <div className="flex flex-col items-center justify-center mt-8">
          <Lightbulb className="size-10 fill-tonal-dark-cream-40 text-tonal-dark-cream-40" />
          <div className="text-tonal-dark-cream-40 p-4">{noResultsMessage}</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col">
        <div className="rounded-3xl w-full overflow-x-auto">
          <table className="w-full text-primary">
            <thead className="bg-tonal-cream-90">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th key={header.id} className="px-6 py-4 text-left text-sm font-normal text-nowrap">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white">
              <tr>
                <td colSpan={columns.length} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center">
                    <Lightbulb className="size-10 fill-tonal-dark-cream-40 text-tonal-dark-cream-40" />
                    <div className="text-tonal-dark-cream-40 p-4">{noResultsMessage}</div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="rounded-3xl w-full overflow-x-auto">
        <table className={cn("w-full text-primary", isLoading && "min-h-80")}>
          <thead className="bg-tonal-cream-90">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th key={header.id} className="px-6 py-4 text-left text-sm font-normal text-nowrap">
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          <tbody className="bg-white">
            {isLoading
              ? Array.from({ length: pageSize }).map((_, idx) => (
                  <tr key={idx} className="h-[52px]">
                    {columns.map((_, cellIdx) => (
                      <td key={cellIdx} className={cn("py-4 px-2", cellIdx === 0 && "pl-5")}>
                        <Skeleton className="h-4 w-[80%]" />
                      </td>
                    ))}
                  </tr>
                ))
              : table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="ml-3 h-[52px] animate-fadeIn">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className={cn("py-4 px-2", cell.column.getIndex() === 0 && "pl-5")}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center gap-2 text-primary mx-auto mt-6">
          {currentPage > 3 && (
            <>
              <span role="button" onClick={() => onPageChange(1)} className="px-2 pb-1 pt-2 rounded cursor-pointer">
                1
              </span>
              {currentPage > 4 && <span className="px-2">...</span>}
            </>
          )}

          {Array.from({ length: totalPages })
            .map((_, i) => i + 1)
            .filter((page) => page >= currentPage - 2 && page <= currentPage + 2)
            .map((pageNumber) => (
              <span
                key={pageNumber}
                role="button"
                onClick={() => !isLoading && onPageChange(pageNumber)}
                className={cn(
                  "px-2 pb-1 pt-2 rounded cursor-pointer",
                  pageNumber === currentPage && "bg-primary text-white",
                  isLoading && "opacity-50"
                )}
              >
                {pageNumber}
              </span>
            ))}

          {currentPage < totalPages - 2 && (
            <>
              {currentPage < totalPages - 3 && <span className="px-2">...</span>}
              <span
                role="button"
                onClick={() => onPageChange(totalPages)}
                className="px-2 pb-1 pt-2 rounded cursor-pointer"
              >
                {totalPages}
              </span>
            </>
          )}
        </div>
      )}
    </div>
  );
}
