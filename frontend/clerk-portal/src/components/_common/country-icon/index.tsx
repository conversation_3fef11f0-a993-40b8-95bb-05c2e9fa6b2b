import Image from "next/image";
import { ComponentProps } from "react";

import { cn } from "@/lib/utils";

interface CountryIconProps extends ComponentProps<"div"> {
  country: { flag?: string; name: string };
}

export function CountryIcon({ country, className, ...props }: CountryIconProps) {
  return (
    <div
      data-tag="country-icon"
      className={cn("relative size-6 rounded-full overflow-hidden flex-none", className)}
      {...props}
    >
      <Image
        className="w-full h-full object-cover"
        src={country.flag ?? ""}
        alt={`${country.name} flag`}
        width={240}
        height={240}
      />
    </div>
  );
}
