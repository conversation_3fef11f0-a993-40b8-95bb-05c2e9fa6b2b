import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";

import Link from "next/link";

export default function Breadcrumb({ paths }: { paths: { href: string; label: string }[] }) {
  return (
    <nav aria-label="breadcrumb" className="w-full flex flex-row  justify-start py-8">
      <ol className="flex items-center space-x-2">
        {paths.map((path, index) => {
          const isLast = index === paths.length - 1;
          return (
            <li key={path.href} className="flex items-center space-x-1 text-sm">
              {isLast ? (
                <span className="text-primary font-bold" aria-hidden>
                  {path.label}
                </span>
              ) : (
                <Link href={path.href} className="flex items-center font-bold">
                  <span className="text-tonal-dark-cream-50 hover:text-tonal-dark-cream-20 text-sm/none">
                    {path.label}
                  </span>
                  <KeyboardArrowRight className="ml-2 fill-tonal-dark-cream-50  text-tonal-dark-cream-50 flex-none w-5 h-5" />
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}
