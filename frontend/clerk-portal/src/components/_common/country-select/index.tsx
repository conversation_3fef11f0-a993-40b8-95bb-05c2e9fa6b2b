"use client";

import { useState } from "react";

import { KeyboardArrowDown, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import { CountryIcon } from "@/components/_common/country-icon";

interface IOpts {
  label: string;
  value: string;
  flag: string;
}

interface ICountrySelectProps {
  label?: string;
  isValid?: boolean;
  opts: IOpts[];
  placeholder?: string;
  errorMessage?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function CountrySelect(props: ICountrySelectProps) {
  const {
    label = "Country",
    isValid = true,
    opts,
    onChange,
    value,
    placeholder = "Select Country",
    errorMessage,
  } = props;

  const [openSelect, setOpenSelect] = useState(false);

  const handleOpenSelect = () => {
    setOpenSelect((current) => !current);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setOpenSelect(false);
    }, 200);
  };

  const handleSelectCountries = (opt: IOpts) => {
    onChange && onChange(opt.value);
    handleOpenSelect();
  };

  const countrySelect = value && opts.find((item) => item.value === value);

  return (
    <div className="relative">
      <p className="text-base font-centra mb-2 text-primary">{label}</p>

      <div
        className={`flex w-full items-center border ${isValid ? "bg-white" : "bg-tonal-red-90"} rounded-2xl ${
          isValid ? "border-tonal-dark-cream-80" : "border-error"
        }`}
      >
        <div
          className={`relative w-[8rem] h-12 cursor-pointer flex flex-1 items-center justify-between p-4`}
          onBlur={handleBlur}
          tabIndex={0}
          onClick={handleOpenSelect}
        >
          {countrySelect ? (
            <div className="flex items-center gap-4">
              <CountryIcon country={{ flag: countrySelect.flag, name: countrySelect.label }} className="size-5" />
              <p className="mt-1 text-primary text-base/none">{countrySelect.label}</p>
            </div>
          ) : (
            <p className="mt-1 text-tonal-dark-cream-60 text-base/none">{placeholder}</p>
          )}
          {openSelect ? (
            <KeyboardArrowUp className="fill-primary" width={20} height={20} />
          ) : (
            <KeyboardArrowDown className="fill-primary" width={20} height={20} />
          )}
        </div>

        {openSelect && (
          <div className="w-full max-h-52 overflow-auto bg-white absolute top-24 rounded-2xl py-3 shadow-elevation-04-1 z-50">
            {opts.map((country, idx) => (
              <button
                className="py-5 px-4 flex items-center justify-between w-full"
                key={idx}
                onClick={() => handleSelectCountries(country)}
                type="button"
              >
                <div className="flex items-center gap-4">
                  <CountryIcon country={{ flag: country.flag, name: country.label }} className="size-7" />
                  <p className="text-primary text-base">{country.label}</p>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {errorMessage && (
        <div className="flex justify-start items-center mt-2.5 space-x-2">
          <span className="font-centra text-sm text-tonal-red-40">{errorMessage}</span>
        </div>
      )}
    </div>
  );
}
