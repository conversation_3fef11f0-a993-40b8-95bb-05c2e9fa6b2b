import type { SVGProps } from "react";
interface SVGRProps {
  title?: string;
  titleId?: string;
}

const Plastic = ({ title, titleId, ...props }: SVGProps<SVGSVGElement> & SVGRProps) => (
  <svg
    width="36"
    height="36"
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-labelledby={titleId}
    {...props}
  >
    {title ? <title id={titleId}>{title}</title> : null}
    <path
      d="M27.2758 5.33951H24.2567V3.46449C24.2567 1.55316 22.795 0 20.9984 0H15.2407C13.4441 0 11.9824 1.55481 11.9824 3.46449V5.33951H8.72248C7.28553 5.33951 6.11914 6.50479 6.11914 7.94407V33.3954C6.11914 34.8331 7.28388 36 8.72248 36H9.681C11.1179 36 12.2843 34.8347 12.2843 33.3954V23.4971C12.2843 21.1567 13.6437 19.1348 15.6152 18.1758V17.6509C15.6152 16.5896 16.3576 15.7033 17.3508 15.4804V12.7306H15.3265C14.8596 12.7306 14.4818 12.3527 14.4818 11.8856C14.4818 11.4185 14.8596 11.0405 15.3265 11.0405H19.7232C22.2259 11.0405 24.2617 13.0773 24.2617 15.5811V15.8155C24.2617 16.2826 23.8839 16.6606 23.417 16.6606C22.9501 16.6606 22.5723 16.2826 22.5723 15.8155V15.5811C22.5723 14.0098 21.2937 12.7306 19.7232 12.7306H19.0385V15.4804C20.0317 15.7033 20.7741 16.5896 20.7741 17.6509V18.1758C22.7455 19.1348 24.1049 21.1567 24.1049 23.4971V33.5407C24.1049 34.7291 23.2883 35.726 22.1863 36H27.2758C28.7128 36 29.8791 34.8347 29.8791 33.3954V7.94407C29.8791 6.50644 28.7144 5.33951 27.2758 5.33951ZM13.7048 5.33951V3.46449C13.7048 2.50387 14.3944 1.72152 15.2407 1.72152H21.0001C21.8481 1.72152 22.536 2.50387 22.536 3.46449V5.33951H13.7048Z"
      fill="#002652"
    />
  </svg>
);

export default Plastic;
