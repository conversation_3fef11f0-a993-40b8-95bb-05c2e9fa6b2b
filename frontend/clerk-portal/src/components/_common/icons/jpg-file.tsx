import { SVGProps } from "react";

interface SVGRProps {
  title?: string;
  titleId?: string;
}

const JpgFile = ({ title, titleId, ...props }: SVGProps<SVGSVGElement> & SVGRProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" fill="none" aria-labelledby={titleId} {...props}>
    {title ? <title id={titleId}>{title}</title> : null}
    <path
      d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
      fill="#1F71FF"
    />
    <path
      d="M20.5421 37.3238C20.5421 39.0638 19.4621 40.1558 17.7461 40.1558C16.4741 40.1558 15.4781 39.4718 15.0581 38.3918L16.6181 37.5278C16.8341 38.0798 17.2301 38.4518 17.7461 38.4518C18.3821 38.4518 18.7181 38.0318 18.7181 37.2758V31.5998H20.5421V37.3238Z"
      fill="#F2F2F2"
    />
    <path
      d="M22.2123 31.5998H25.0683C26.8923 31.5998 28.1523 32.5838 28.1523 34.3118C28.1523 36.0278 26.8923 37.0238 25.0683 37.0238H24.0123V39.9998H22.2123V31.5998ZM24.0123 33.2198V35.4278H25.1043C25.8243 35.4278 26.3403 35.0198 26.3403 34.3238C26.3403 33.6278 25.8243 33.2198 25.1043 33.2198H24.0123Z"
      fill="#F2F2F2"
    />
    <path
      d="M37.2073 33.6158L35.5753 34.4078C35.1553 33.6038 34.3753 33.1478 33.4033 33.1478C31.9753 33.1478 30.8473 34.3238 30.8473 35.7998C30.8473 37.2758 31.9753 38.4638 33.4153 38.4638C34.5313 38.4638 35.4073 37.8038 35.6233 36.8438H33.1873V35.3318H37.5553V36.0038C37.5553 38.3438 35.8033 40.1558 33.4033 40.1558C30.9553 40.1558 29.0233 38.1998 29.0233 35.7998C29.0233 33.3998 31.0033 31.4438 33.4153 31.4438C35.0953 31.4438 36.5233 32.2718 37.2073 33.6158Z"
      fill="#F2F2F2"
    />
  </svg>
);
export default JpgFile;
