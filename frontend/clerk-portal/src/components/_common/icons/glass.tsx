import type { SVGProps } from "react";
interface SVGRProps {
  title?: string;
  titleId?: string;
}

const Glass = ({ title, titleId, ...props }: SVGProps<SVGSVGElement> & SVGRProps) => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-labelledby={titleId}
    {...props}
  >
    {title ? <title id={titleId}>{title}</title> : null}
    <path
      d="M36 20.204C35.9595 17.7266 34.6738 15.5503 32.7335 14.2558C31.8678 13.6791 31.3603 12.7046 31.3603 11.6732V0.816663C31.3603 0.366235 30.9893 0 30.533 0H26.6481C26.1918 0 25.8208 0.366235 25.8208 0.816663V11.6859C25.8208 12.7193 25.3005 13.6854 24.4349 14.2663C23.0873 15.1693 22.0574 16.4974 21.5436 18.055C21.3666 17.6951 21.1854 17.3373 20.9935 16.9836C20.4284 15.9418 20.132 14.7778 20.132 13.597V6.94164C20.132 6.26389 19.5755 5.71454 18.889 5.71454H17.245C16.5585 5.71454 16.0019 6.26389 16.0019 6.94164V13.597C16.0019 14.7778 15.7056 15.9418 15.1405 16.9836C12.7631 21.3679 11.52 26.2637 11.52 31.2374V43.8388V44.0766H11.5264C11.6288 46.7603 13.4838 47.7201 16.0233 48V29.9071C16.0233 28.5853 17.1086 27.5119 18.4497 27.5119H18.6544V22.6666C18.6544 22.1109 19.1107 21.6605 19.6736 21.6605H21.4498C22.0127 21.6605 22.469 22.1109 22.469 22.6666V27.5119H22.6737C24.0127 27.5119 25.1001 28.5832 25.1001 29.9071V43.7041C25.1001 46.6971 27.4456 47.9874 30.0426 47.9874H31.049C32.7505 47.9874 34.3433 47.4338 35.2409 46.2088C35.2452 46.2025 35.2495 46.1983 35.2516 46.1941C35.2708 46.1667 35.29 46.1372 35.3092 46.1078C35.4286 45.9289 35.5331 45.7415 35.6226 45.5458C35.8572 45.0238 35.9894 44.4113 35.9894 43.7041L35.9979 20.2061L36 20.204Z"
      fill="#002652"
    />
  </svg>
);

export default Glass;
