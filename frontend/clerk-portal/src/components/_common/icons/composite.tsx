import type { SVGProps } from "react";
interface SVGRProps {
  title?: string;
  titleId?: string;
}

const Composite = ({ title, titleId, ...props }: SVGProps<SVGSVGElement> & SVGRProps) => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-labelledby={titleId}
    {...props}
  >
    {title ? <title id={titleId}>{title}</title> : null}
    <path
      d="M36.9964 9.30011L30.7635 2.99673V1.09785C30.7635 0.491088 30.2779 0 29.6779 0H28.2168C27.6168 0 27.1312 0.491088 27.1312 1.09785V3.27392L24.7658 5.66606H15.4229C14.2036 5.66606 13.2173 6.6657 13.2173 7.89669V11.5853H15.5675V12.9625H23.3414V11.5853H24.7378C25.3032 11.5853 25.8277 11.8931 26.1061 12.3907L29.311 18.0939H9.6001V44.8156C9.6001 46.5748 11.0094 48 12.7489 48H26.1622C27.9017 48 29.311 46.5748 29.311 44.8156V44.3485H34.7713C36.5108 44.3485 37.9201 42.9232 37.9201 41.1641V11.5526C37.9201 10.7079 37.5877 9.89814 36.9985 9.30011H36.9964ZM19.4545 8.61695C21.6019 8.61695 23.3414 9.12332 23.3414 9.74973C23.3414 10.3761 21.6019 10.8825 19.4545 10.8825C17.3071 10.8825 15.5675 10.3761 15.5675 9.74973C15.5675 9.12332 17.3071 8.61695 19.4545 8.61695Z"
      fill="#002652"
    />
  </svg>
);

export default Composite;
