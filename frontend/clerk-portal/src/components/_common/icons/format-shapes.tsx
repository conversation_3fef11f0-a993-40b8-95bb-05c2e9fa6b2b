import * as React from "react";
import type { SVGProps } from "react";
interface SVGRProps {
  title?: string;
  titleId?: string;
}
const FormatShapes = ({ title, titleId, ...props }: SVGProps<SVGSVGElement> & SVGRProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" aria-labelledby={titleId} {...props}>
    {title ? <title id={titleId}>{title}</title> : null}
    <path
      d="M24.9163 6.50016V2.16683C24.9163 1.571 24.4288 1.0835 23.833 1.0835H19.4997C18.9038 1.0835 18.4163 1.571 18.4163 2.16683V3.25016H7.58301V2.16683C7.58301 1.571 7.09551 1.0835 6.49967 1.0835H2.16634C1.57051 1.0835 1.08301 1.571 1.08301 2.16683V6.50016C1.08301 7.096 1.57051 7.5835 2.16634 7.5835H3.24967V18.4168H2.16634C1.57051 18.4168 1.08301 18.9043 1.08301 19.5002V23.8335C1.08301 24.4293 1.57051 24.9168 2.16634 24.9168H6.49967C7.09551 24.9168 7.58301 24.4293 7.58301 23.8335V22.7502H18.4163V23.8335C18.4163 24.4293 18.9038 24.9168 19.4997 24.9168H23.833C24.4288 24.9168 24.9163 24.4293 24.9163 23.8335V19.5002C24.9163 18.9043 24.4288 18.4168 23.833 18.4168H22.7497V7.5835H23.833C24.4288 7.5835 24.9163 7.096 24.9163 6.50016ZM3.24967 3.25016H5.41634V5.41683H3.24967V3.25016ZM5.41634 22.7502H3.24967V20.5835H5.41634V22.7502ZM18.4163 20.5835H7.58301V19.5002C7.58301 18.9043 7.09551 18.4168 6.49967 18.4168H5.41634V7.5835H6.49967C7.09551 7.5835 7.58301 7.096 7.58301 6.50016V5.41683H18.4163V6.50016C18.4163 7.096 18.9038 7.5835 19.4997 7.5835H20.583V18.4168H19.4997C18.9038 18.4168 18.4163 18.9043 18.4163 19.5002V20.5835ZM22.7497 22.7502H20.583V20.5835H22.7497V22.7502ZM20.583 5.41683V3.25016H22.7497V5.41683H20.583ZM14.018 8.28766C13.8555 7.86516 13.4438 7.5835 12.9888 7.5835C12.5338 7.5835 12.1222 7.86516 11.9705 8.28766L8.96967 16.2285C8.76384 16.7593 9.15384 17.3335 9.72801 17.3335C10.0747 17.3335 10.378 17.1168 10.4972 16.7918L11.093 15.1668H14.8738L15.4805 16.8027C15.5997 17.1168 15.903 17.3335 16.2497 17.3335H16.2605C16.8347 17.3335 17.2247 16.7593 17.0297 16.2285L14.018 8.28766ZM11.5805 13.8018L12.9997 9.65266L14.408 13.8018H11.5805Z"
      fill="#F5F5F5"
    />
  </svg>
);
export default FormatShapes;
