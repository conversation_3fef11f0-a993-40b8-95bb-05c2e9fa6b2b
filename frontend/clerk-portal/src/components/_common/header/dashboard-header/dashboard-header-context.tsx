"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface DashboardHeaderContextType {
  isDrawerOpen: boolean;
  toggleDrawer: () => void;
  closeDrawer: () => void;
  openDrawer: () => void;
}

const DashboardHeaderContext = createContext<DashboardHeaderContextType | undefined>(undefined);

export function useDashboardHeader() {
  const context = useContext(DashboardHeaderContext);
  if (!context) {
    throw new Error("useDashboardHeader must be used within a DashboardHeaderProvider");
  }
  return context;
}

interface DashboardHeaderProviderProps {
  children: ReactNode;
}

export function DashboardHeaderProvider({ children }: DashboardHeaderProviderProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const toggleDrawer = () => setIsDrawerOpen((prev) => !prev);
  const closeDrawer = () => setIsDrawerOpen(false);
  const openDrawer = () => setIsDrawerOpen(true);

  return (
    <DashboardHeaderContext.Provider
      value={{
        isDrawerOpen,
        toggleDrawer,
        closeDrawer,
        openDrawer,
      }}
    >
      {children}
    </DashboardHeaderContext.Provider>
  );
}
