"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useDebouncedCallback } from "use-debounce";

import { Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { cn } from "@/lib/utils";

interface SearchInputProps extends React.ComponentProps<typeof Input> {
  queryName?: string;
  onSearch?: (value: string) => void;
}

export function SearchInput(props: SearchInputProps) {
  const { queryName = "q", placeholder = "Search", className, ...restProps } = props;

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const query = searchParams.get(queryName) ?? "";

  const debounced = useDebouncedCallback((value: string) => {
    const term = value.trim();

    if (props.onSearch) {
      props.onSearch?.(term);
    } else {
      const params = new URLSearchParams(searchParams.toString());
      params.set(queryName, term);
      router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, 1000);

  return (
    <Input
      {...restProps}
      leftIcon={<Search width={20} height={20} className="fill-primary" />}
      placeholder={placeholder}
      // type='search'
      name={queryName}
      defaultValue={query}
      onChange={(event: React.ChangeEvent<HTMLInputElement>) => debounced(event.target.value)}
      // onFocus={handleFocus}
      // onBlur={handleBlur}
      className={cn("rounded-3xl", className)}
      style={{ borderRadius: 24 }}
    />
  );
}
