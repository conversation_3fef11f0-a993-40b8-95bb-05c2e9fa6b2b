import React, { InputHTMLAttributes } from "react";

function maskNumber(value: string): string {
  const cleanedValue = value.replace(/\D/g, "");

  if (!cleanedValue.length) return "0,00";

  const paddedValue = cleanedValue.padStart(3, "0");

  let result = paddedValue.slice(0, -2) + "," + paddedValue.slice(-2);

  result = result.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

  return result;
}

function unmaskNumber(maskedValue: string): number {
  if (!maskedValue || maskedValue === "0,00") return 0;

  const unmaskedString = maskedValue.replace(/[.,]/g, "");

  return parseInt(unmaskedString, 10);
}

interface PriceInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  onChange?: (value: number) => void;
  value?: number;
}

export function PriceInput({ onChange, value, ...props }: PriceInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    const numericValue = unmaskNumber(value);

    onChange?.(numericValue);
  };

  const secondDisplayName = maskNumber(String(value || 0)) !== "0,00" ? maskNumber(String(value || 0)) : "";

  return (
    <input
      type="text"
      value={secondDisplayName}
      onChange={handleChange}
      placeholder="0.000,00"
      className="text-right bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80 block w-full border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary invalid:border-error invalid:bg-tonal-red-90 data-[invalid=true]:border-error data-[invalid=true]:bg-tonal-red-90 focus:data-[invalid=true]:outline-error"
      {...props}
    />
  );
}
