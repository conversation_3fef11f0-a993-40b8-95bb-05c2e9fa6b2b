{"name": "lizenzero-clerk-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "format": "prettier --write .", "check-all": "pnpm run check-format && npm run check-lint && npm run check-types && npm run build", "prepare": "cd ../.. && husky install frontend/clerk-portal/.husky"}, "dependencies": {"@arthursenno/lizenzero-ui-react": "3.0.0", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-pdf/renderer": "^4.1.5", "@tanstack/react-query": "^5.61.5", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.446.0", "next": "14.0.4", "next-auth": "^4.24.7", "next-intl": "^4.3.1", "notistack": "^3.0.1", "react": "^18", "react-countup": "^6.5.3", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.51.1", "react-input-mask": "^2.0.4", "react-number-format": "^5.4.2", "server-only": "^0.0.1", "string-similarity": "^4.0.4", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.3", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/accept-language-parser": "^1.5.6", "@types/axios": "^0.14.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-input-mask": "^3.0.5", "@types/string-similarity": "^4.0.2", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "autoprefixer": "^10.0.1", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^42.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.33.2", "husky": "^8.0.3", "postcss": "^8", "prettier": "3.1.1", "tailwindcss": "^3.3.0", "typescript": "^5.3.3"}}