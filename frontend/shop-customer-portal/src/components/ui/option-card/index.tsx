interface OptionCardProps {
  label: string;
  value: "YES" | "NO";
  name: string;
  checked: boolean;
  onChange: (value: "YES" | "NO") => void;
}

export const OptionCard = ({ label, value, name, checked, onChange }: OptionCardProps) => {
  return (
    <label
      className={`flex-1 flex items-center justify-between cursor-pointer p-4 rounded-2xl border-2 transition-all duration-200 group ${
        checked ? "border-primary bg-light-blue" : "border-tonal-dark-cream-80 bg-white hover:border-primary"
      }`}
    >
      <span className={`text-lg text-primary group-hover:text-primary ${checked ? "font-medium" : ""}`}>{label}</span>
      <div className="relative">
        <input
          type="radio"
          name={name}
          value={value}
          checked={checked}
          onChange={() => onChange(value)}
          className="appearance-none w-6 h-6 border-2 border-tonal-dark-cream-80 rounded-full checked:border-primary checked:bg-primary relative"
        />
        {checked && (
          <div className="absolute inset-0 w-6 h-6 rounded-full border-2 border-primary bg-primary flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        )}
      </div>
    </label>
  );
};
