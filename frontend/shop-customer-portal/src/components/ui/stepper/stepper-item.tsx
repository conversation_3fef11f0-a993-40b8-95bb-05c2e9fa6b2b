interface StepperItemProps {
  step: number;
  title: string;
  checked?: boolean;
}

export function StepperItem({ step, title, checked = false }: StepperItemProps) {
  const isFirstItem = step === 1;

  return (
    <div className="group flex flex-1 items-start" data-first={isFirstItem} data-checked={checked}>
      {!isFirstItem && (
        <div
          className={`hidden lg:flex h-[1px] flex-grow rounded-sm ${
            checked ? "bg-[#1B6C64]" : "bg-tonal-dark-cream-80"
          } mt-3.5`}
        />
      )}
      <div className="flex flex-col items-center">
        <div className="flex justify-center items-center h-7 w-7 rounded-full bg-tonal-dark-cream-80 group-data-[checked=true]:bg-[#1B6C64]">
          <p className="text-base text-tonal-dark-cream-20 group-data-[first=true]:text-white group-data-[checked=true]:text-white">
            {step}
          </p>
        </div>
        <p className="hidden lg:block mt-2 text-center text-sm text-tonal-dark-cream-20 group-data-[checked=true]:text-primary group-data-[checked=true]:font-bold leading-tight">
          {title}
        </p>
      </div>
    </div>
  );
}
