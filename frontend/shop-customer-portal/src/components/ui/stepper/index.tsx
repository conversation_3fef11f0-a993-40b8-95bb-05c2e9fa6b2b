import { cn } from "@/lib/utils";
import { StepperItem } from "./stepper-item";

interface StepperProps {
  steps: Array<{ index: number; title: string }>;
  currentStep: number;
  className?: string;
}

export function Stepper({ steps, currentStep, className }: StepperProps) {
  return (
    <div className={cn("w-full flex items-start gap-2 lg:gap-4 pb-6", className)}>
      {steps.map((step) => (
        <StepperItem
          key={`stepper_item_${step.index}`}
          step={step.index}
          title={step.title}
          checked={currentStep >= step.index}
        />
      ))}
    </div>
  );
}
