"use client";

import React, { Fragment, PropsWithChildren, useCallback, useMemo, useState, memo } from "react";
import { formatCurrency } from "@/utils/formatCurrency";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { EditCircle, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Country } from "@/lib/api/country/types";
import { CountryIcon } from "@/components/_common/country-icon";

// Mock data for testing
const mockCountrySection: CountrySection[] = [
  {
    country: {
      id: 1,
      name: "Germany",
      code: "DE",
      flag_url: "https://cdn.kcak11.com/CountryFlags/countries/de.svg",
      authorize_representative_obligated: true,
      other_costs_obligated: false,
      is_published: true,
      license_required: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    startYear: 2024,
    price: 29900,
    serviceName: "EPR Compliance Packaging",
  },
  {
    country: {
      id: 2,
      name: "France",
      code: "FR",
      flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fr.svg",
      authorize_representative_obligated: true,
      other_costs_obligated: false,
      is_published: true,
      license_required: false,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    startYear: 2025,
    price: 3000,
    serviceName: "EPR Compliance Packaging",
  },
  {
    country: {
      id: 3,
      name: "Netherlands",
      code: "NL",
      flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nl.svg",
      authorize_representative_obligated: false,
      other_costs_obligated: true,
      is_published: true,
      license_required: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    startYear: 2026,
    price: 29900,
    serviceName: "EPR Compliance Packaging",
  },
];

const mockNetTotalSection: NetTotalSection = {
  bundleDiscountOff: 0.0,
  bundleDiscountLabel: "Bundle discount (3 countries - 5% off)",
};

const mockTotalSection: TotalSection = {
  vat: 0.0,
  vatLabel: "VAT 19%",
};

/**
 * TODO: get data from shopping card
 * mock data for dev/testing purpose only
 */
interface JourneyShoppingCartCardProps {
  defaultLayout?: "large" | "mini";
}
export const JourneyShoppingCartCard = memo(function JourneyShoppingCartCard({
  defaultLayout = "large",
}: JourneyShoppingCartCardProps) {
  const t = useTranslations("shop.common.cartCard");
  return (
    <ShoppingCardView
      enableEdit={true}
      defaultLayout={defaultLayout}
      countrySection={mockCountrySection}
      netTotalSection={mockNetTotalSection}
      totalSection={mockTotalSection}
      note={t("estimatedCostsNote")}
    />
  );
});

interface CountrySection {
  country: Country;
  startYear: number;
  price: number;
  serviceName: string;
}

interface NetTotalSection {
  bundleDiscountOff: number;
  bundleDiscountLabel: string;
}
interface TotalSection {
  vat: number;
  vatLabel: string;
}

interface ShoppingCardViewProps {
  enableEdit?: boolean;
  defaultLayout: "large" | "mini";
  countrySection?: CountrySection[];
  netTotalSection?: NetTotalSection;
  totalSection?: TotalSection;
  note?: string;
}
const ShoppingCardView = memo(function ShoppingCardView(props: ShoppingCardViewProps) {
  const { enableEdit, defaultLayout, countrySection, netTotalSection, totalSection, note } = props;
  const t = useTranslations("shop.common.cartCard");
  const [showMore, setShowMore] = useState(defaultLayout === "large");

  const handleShowMoreClick = () => {
    setShowMore(!showMore);
  };
  const onEdit = useCallback(() => {
    location.href = "./select-services";
  }, []);

  const countriesTotal = useMemo(() => {
    return (countrySection || []).reduce(
      (total, { price, startYear }) => total + getPriceForCountry(price, startYear),
      0
    );
  }, [countrySection]);

  const bundleDiscount = useMemo(() => {
    return countriesTotal * (netTotalSection?.bundleDiscountOff || 0);
  }, [countriesTotal, netTotalSection]);

  const netTotal = useMemo(() => {
    return countriesTotal - bundleDiscount;
  }, [countriesTotal, bundleDiscount]);

  const vat = useMemo(() => {
    return Math.ceil(netTotal * (totalSection?.vat || 0));
  }, [netTotal, totalSection]);
  const total = useMemo(() => {
    return netTotal + vat;
  }, [netTotal, vat]);

  return (
    <CartLayout>
      <CartHeader showMore={showMore} onShowMoreToggle={handleShowMoreClick} onEdit={enableEdit ? onEdit : null} />
      {/* Country section */}
      {showMore
        ? (countrySection || []).map((data) => {
            const { country, serviceName, startYear, price } = data;
            const isFutureYear = startYear > new Date().getFullYear();
            return (
              <CartSection key={data.country.id} separatorOffset="normal">
                <CartSectionItem>
                  <div className="flex flex-row items-center gap-3">
                    <CountryIcon country={{ name: country.name, flag_url: country.flag_url }} className="size-6" />
                    <h3 className="text-primary font-bold">{country.name}</h3>
                  </div>
                </CartSectionItem>
                <CartSectionItem>
                  <div className="flex flex-col gap-1">
                    <p className="text-primary">{serviceName}</p>
                    {/* TODO: enable this, no needed for now according to ticket 1EPR-408 */}
                    {/* <p className="text-tonal-dark-cream-40">
                  {formatCurrency(price)} / year starting {startYear}
                </p>
                {isFutureYear ? (
                  <p className="text-tonal-dark-cream-40 italic mt-4 pr-3">
                    The {startYear} annual fee is charged now and won’t be charged again next year.
                  </p>
                ) : null} */}
                  </div>
                  <p className="text-primary font-bold">{formatCurrency(getPriceForCountry(price, startYear))}</p>
                </CartSectionItem>
              </CartSection>
            );
          })
        : null}
      {/* Net total section */}
      {/* TODO: enable this, no needed for now according to ticket 1EPR-408 */}
      {/* {showMore && netTotalSection ? (
        <CartSection separatorOffset="none">
          <CartSectionItem>
            <p className="text-primary">{netTotalSection.bundleDiscountLabel}</p>
            <p className="text-primary font-bold">{formatCurrency(-bundleDiscount)}</p>
          </CartSectionItem>
          <CartSectionItem>
            <p className="text-primary text-base font-bold">Net total</p>
            <p className="text-primary text-base font-bold">{formatCurrency(netTotal)}</p>
          </CartSectionItem>
        </CartSection>
      ) : null} */}
      {/* Total section */}
      {totalSection ? (
        <CartSection showSeparator={false} showBorder={false}>
          {/* TODO: enable this, no needed for now according to ticket 1EPR-408 */}
          {/* <CartSectionItem>
            <p className="text-primary">{totalSection.vatLabel}</p>
            <p className="text-primary font-bold">{formatCurrency(vat)}</p>
          </CartSectionItem> */}
          <CartSectionItem highlight={true} single={true}>
            <p className="text-primary text-xl font-bold">{t("netTotal")}</p>
            <p className="text-primary text-xl font-bold">{formatCurrency(total)}</p>
          </CartSectionItem>
        </CartSection>
      ) : null}
      {/* Note */}
      {note ? (
        <div className="p-4 bg-surface-01 rounded-[20px] flex flex-row items-center gap-3">
          <Error className="fill-primary size-6" />
          <p className="text-primary text-base leading-5">{note}</p>
        </div>
      ) : null}
    </CartLayout>
  );
});

function getPriceForCountry(price: number, startYear: number) {
  const year = new Date().getFullYear();
  const years = Math.max(0, year - startYear) + 1;
  return years * price;
}

type CartLayoutProps = PropsWithChildren;
const CartLayout = memo(function CartLayout(props: CartLayoutProps) {
  const { children } = props;
  return (
    <div id="cart-details" className="flex flex-row items-center relative w-[520px]">
      <div className="flex flex-col gap-6 drop-shadow-md bg-white w-full px-4 py-6 md:p-7 md:pr-10 rounded-4xl">
        {children}
      </div>
      <div className="hidden md:block">
        <div className="absolute top-[40px]">
          <div className="bg-tonal-dark-blue-90 w-12 h-12 rounded-full absolute z-10 my-auto  right-2 top-0 bottom-0"></div>
          <svg
            className="drop-shadow-[5px_2px_2px_rgba(0,0,0,0.05)]"
            width="40"
            height="128"
            viewBox="0 0 40 128"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M-1.52638e-06 0C-1.38053e-06 12.2314 7.07429 22.8064 17.3429 27.8331C17.3886 27.856 17.2971 27.8101 17.3429 27.8331C30.6743 34.3385 40 48.129 40 64C40 79.871 30.6743 93.6615 17.3429 100.167C17.2971 100.19 17.3886 100.144 17.3429 100.167C7.07429 105.194 -1.45858e-07 115.769 0 128L-1.52638e-06 0Z"
              fill="white"
            />
          </svg>
        </div>
      </div>
    </div>
  );
});

interface CartHeaderProps {
  showMore: boolean;
  onShowMoreToggle: () => void;
  onEdit?: (() => void) | null;
}
const CartHeader = memo(function CartHeader(props: CartHeaderProps) {
  const { showMore, onShowMoreToggle, onEdit } = props;
  const t = useTranslations("shop.common.cartCard");
  return (
    <div className="flex flex-row justify-between items-center">
      <div className="flex flex-row items-center gap-4">
        <p className="text-primary text-3xl font-bold">{t("cart")}</p>
        {onEdit ? (
          <Button
            type="button"
            variant="text"
            color="light-blue"
            size="small"
            leadingIcon={<EditCircle />}
            onClick={onEdit}
          >
            {t("editServices")}
          </Button>
        ) : null}
      </div>
      <Button
        type="button"
        variant="text"
        color="light-blue"
        size="small"
        onClick={onShowMoreToggle}
        aria-expanded={showMore}
        aria-controls="cart-details"
      >
        {showMore ? t("showLess") : t("showMore")}
      </Button>
    </div>
  );
});

type CartSectionProps = PropsWithChildren<{
  showSeparator?: boolean;
  separatorOffset?: "none" | "normal";
  showBorder?: boolean;
}>;
const CartSection = memo(function CartSection(props: CartSectionProps) {
  const { children, showSeparator = true, separatorOffset = "none", showBorder = true } = props;
  return (
    <div className={cn("flex flex-col rounded-xl border-tonal-cream-90", showBorder ? "border" : "")}>
      {React.Children.toArray(children).map((row, i, list) => {
        const shouldAddSeparator = showSeparator && i > 0 && list.length > 1;
        return (
          <Fragment key={i}>
            {shouldAddSeparator ? (
              <hr className={cn("text-tonal-cream-90", separatorOffset === "normal" ? "mx-4" : "")} />
            ) : null}
            {row}
          </Fragment>
        );
      })}
    </div>
  );
});
interface CartSectionItemProps {
  children: React.ReactNode | [React.ReactNode, React.ReactNode];
  highlight?: boolean;
  single?: boolean;
}
const CartSectionItem = memo(function CartSectionItem(props: CartSectionItemProps) {
  const { children, highlight, single } = props;
  const childrenList = React.Children.toArray(children);
  const left = childrenList[0];
  const right = childrenList[1];
  return (
    <div
      className={cn(
        "self-stretch py-3 px-4 inline-flex justify-start items-center gap-3",
        highlight ? "bg-tonal-blue-90 -ml-px -mr-px -mb-px rounded-bl-xl rounded-br-xl" : "",
        single ? "rounded-xl" : ""
      )}
    >
      <div className="flex-1 py-4 self-stretch flex justify-start items-center gap-2">
        <div className="flex-1 flex justify-start items-center gap-2">{left}</div>
        {right ? <div className="inline-flex flex-col justify-center items-start gap-2"> {right} </div> : null}
      </div>
    </div>
  );
});
