"use client";

import { Checkbox } from "@/components/_common/checkbox";
import { Divider } from "@/components/_common/divider";
import { CreateAccountSchema } from "@/components/_common/forms/schemas";
import { FormInputIcon } from "@/components/_common/input-status-icon";
import { LoginModal } from "@/components/_common/modals/login-modal";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { PasswordInput } from "@/components/ui/password-input";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { createAccount } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { getConsentsByType } from "@/lib/api/consent";
import { ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { JourneyAccountVerificationModal } from "../journey-account-verification-modal";

export function JourneyCreateAccount() {
  const { shoppingCart } = useShoppingCart();
  const { changeParam } = useQueryFilter(["verify-account"]);
  const t = useTranslations("shop.common.journey.createAccount");
  const globalT = useTranslations("global");

  const verificationMode = shoppingCart.journey === "LONG" ? "MAGIC-LINK" : "CODE";

  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const createAccountSchema = CreateAccountSchema.extend({
    enablePassword: z.boolean().default(false),
    consents: z.array(
      z.object({
        id: z.number(),
        name: z.string(),
        description: z.string(),
        isChecked: z.boolean(),
      })
    ),
  }).superRefine((data, ctx) => {
    if (data.enablePassword) {
      if (!data.password) {
        ctx.addIssue({ path: ["password"], code: z.ZodIssueCode.custom, message: globalT("validation.required") });
      }

      if (!data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          code: z.ZodIssueCode.custom,
          message: globalT("validation.required"),
        });
      }

      if (data.password !== data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          code: z.ZodIssueCode.custom,
          message: t("form.matchPassword"),
        });
      }
    }

    const consents = Object.values(data.consents || {});

    if (!!consents.length && consents.some((consent) => !consent.isChecked)) {
      ctx.addIssue({ path: ["consents"], code: z.ZodIssueCode.custom, message: t("form.consent.allChecked") });
    }
  });

  type CreateAccountFormData = z.infer<typeof createAccountSchema>;

  const consentListQuery = useQuery({
    queryKey: ["consent-list", "account"],
    queryFn: async () => {
      const consents = await getConsentsByType("ACCOUNT");

      if (!consents) throw new Error("Unable to fetch consent list");

      form.setValue(
        "consents",
        consents.map((consent) => ({
          id: consent.id,
          name: consent.name,
          description: consent.description,
          isChecked: false,
        }))
      );

      return consents;
    },
  });

  const form = useForm<CreateAccountFormData>({
    resolver: zodResolver(createAccountSchema),
    mode: "all",
    defaultValues: {
      consents: [],
    },
  });

  async function handleFormSubmit(data: CreateAccountFormData) {
    try {
      const createAccountResponse: any = await createAccount({
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        company_name: data.companyName,
        password: data.password,
      });

      if (!createAccountResponse) throw new Error();

      if ([400, 409].includes(createAccountResponse?.response?.status)) {
        enqueueSnackbar(t("errors.emailInUse"), { variant: "error" });
        const errorMessage = createAccountResponse?.response?.data?.message;

        // TODO: i18n
        if (errorMessage !== `Check your email and confirm your account before continuing.`) {
          form.setError("email", {
            message: t("errors.emailInUse"),
            type: "already-exists",
          });
        }

        enqueueSnackbar(errorMessage || "Error creating user", { variant: "error" });

        return;
      }

      if (!createAccountResponse?.data) throw new Error();

      changeParam("verify-account", "true", { scroll: false });

      enqueueSnackbar(t("success"), { variant: "success" });
    } catch (err) {
      enqueueSnackbar(t("failed"), { variant: "error" });
    }
  }

  const email = useWatch({ control: form.control, name: "email" });
  const isPasswordEnabled = useWatch({ control: form.control, name: "enablePassword" });
  const password = useWatch({ control: form.control, name: "password" });
  const confirmPassword = useWatch({ control: form.control, name: "confirmPassword" });
  const consents = useWatch({ control: form.control, name: "consents" });
  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;
  const errors = form.formState.errors;

  return (
    <>
      <div className="w-full grid grid-cols-1 md:grid-cols-12 gap-6 justify-center mb-20">
        <form className="w-full md:col-span-7" onSubmit={form.handleSubmit(handleFormSubmit)}>
          <div className="col-span-7 w-full">
            <div className="w-full rounded-[32px] items-start bg-surface-01 flex flex-col py-7 px-4 md:px-8">
              <p className="text-[#808FA9] font-light text-sm mb-8">*{t("mandatoryFields")}</p>
              <div className="w-full ">
                <Input
                  {...form.register("email")}
                  label={t("form.email.label") + "*"}
                  placeholder={t("form.email.placeholder")}
                  variant={errors.email && "error"}
                  errorMessage={
                    errors.email &&
                    (errors.email.type === "already-exists" ? (
                      <p className="flex gap-1">
                        {errors.email.message}.
                        <span className="font-bold underline cursor-pointer" onClick={() => setIsLoginModalOpen(true)}>
                          {t("form.email.login")}.
                        </span>
                        <QuestionTooltip className="fill-error hover:fill-on-error-container">
                          <QuestionTooltipDescription>{t("form.email.tooltip")}</QuestionTooltipDescription>
                        </QuestionTooltip>
                      </p>
                    ) : (
                      errors.email.message
                    ))
                  }
                  rightIcon={<FormInputIcon control={form.control} name="email" />}
                  enabled={!form.formState.isSubmitting}
                />
              </div>
              <Divider />
              <div className="grid md:grid-cols-2 w-full gap-6 md:gap-8">
                <Input
                  {...form.register("firstName")}
                  label={t("form.firstName.label") + "*"}
                  placeholder={t("form.firstName.placeholder")}
                  variant={errors.firstName && "error"}
                  errorMessage={errors.firstName && errors.firstName.message}
                  rightIcon={<FormInputIcon control={form.control} name="firstName" />}
                  enabled={!form.formState.isSubmitting}
                />
                <Input
                  {...form.register("lastName")}
                  label={t("form.lastName.label") + "*"}
                  placeholder={t("form.lastName.placeholder")}
                  variant={errors.lastName && "error"}
                  errorMessage={errors.lastName && errors.lastName.message}
                  rightIcon={<FormInputIcon control={form.control} name="lastName" />}
                  enabled={!form.formState.isSubmitting}
                />
                <Input
                  {...form.register("companyName")}
                  label={t("form.companyName.label") + "*"}
                  placeholder={t("form.companyName.placeholder")}
                  variant={errors.companyName && "error"}
                  errorMessage={errors.companyName && errors.companyName.message}
                  rightIcon={<FormInputIcon control={form.control} name="companyName" />}
                  enabled={!form.formState.isSubmitting}
                />
              </div>

              {shoppingCart.journey === "LONG" && (
                <>
                  <Divider />
                  <div className="space-y-4">
                    <Controller
                      control={form.control}
                      name="enablePassword"
                      disabled={form.formState.isSubmitting}
                      render={({ field }) => (
                        <Checkbox
                          label={t("form.createAccount.label")}
                          description={t("form.createAccount.description")}
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                          disabled={form.formState.isSubmitting}
                        />
                      )}
                    />
                    {isPasswordEnabled && (
                      <div className="space-y-5 w-full gap-8">
                        <div className="space-y-2">
                          <PasswordInput
                            {...form.register("password")}
                            label={t("form.password.placeholder")}
                            placeholder={t("form.password.placeholder")}
                            enabled={!form.formState.isSubmitting}
                            variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
                            errorMessage={errors.password && errors.password.message}
                          />
                          <PasswordStrengthBar password={password} />
                        </div>
                        <PasswordInput
                          {...form.register("confirmPassword")}
                          label={t("form.confirmPassword.placeholder")}
                          placeholder={t("form.confirmPassword.placeholder")}
                          enabled={!form.formState.isSubmitting}
                          variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
                          errorMessage={
                            (errors.confirmPassword && errors.confirmPassword.message) ||
                            (isNotValidConfirmPassword && t("form.passwordMismatch"))
                          }
                        />
                      </div>
                    )}
                  </div>
                </>
              )}
              <Divider />
              <div className="space-y-4">
                {!!errors.consents && <p className="text-tonal-red-40 text-sm">{errors.consents.root?.message}</p>}
                {consents.map((consent, consentIndex) => (
                  <Controller
                    key={consent.id}
                    control={form.control}
                    disabled={form.formState.isSubmitting}
                    name={`consents.${consentIndex}`}
                    render={({ field }) => (
                      <Checkbox
                        label={consent?.name || ""}
                        description={consent?.description}
                        checked={field.value?.isChecked}
                        onChange={(e) => field.onChange({ ...field.value, isChecked: e.target.checked })}
                        disabled={form.formState.isSubmitting}
                      />
                    )}
                  />
                ))}
              </div>
              <div className="flex flex-col md:flex-row md:items-center md:justify-end gap-4 w-full mt-5">
                <Button
                  color="dark-blue"
                  variant="outlined"
                  size="medium"
                  onClick={() => setIsLoginModalOpen(true)}
                  type="button"
                  disabled={form.formState.isSubmitting}
                  style={{ textWrap: "nowrap" }}
                  className="w-full md:w-auto"
                >
                  {t("form.alreadyHaveAccount")}
                </Button>
                <Button
                  color={!!Object.keys(errors).length ? "red" : "yellow"}
                  variant="filled"
                  size="medium"
                  disabled={consentListQuery.isLoading || form.formState.isSubmitting}
                  trailingIcon={!form.formState.isSubmitting && <East />}
                  className="w-full md:w-auto"
                >
                  {form.formState.isSubmitting ? t("form.button.loading") : t("form.button.continue")}
                </Button>
              </div>
            </div>
          </div>
        </form>
        <div className="w-full md:col-span-5">
          <ShopBanner className="!w-full !h-auto" title="">
            <div className="space-y-6">
              {/* Quick and easy benefit */}
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-12 h-12 bg-[#1E3A5F] rounded-full flex items-center justify-center">
                  <Lightbulb width={24} height={24} className="fill-[#A9C8FF]" />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-base">{t("benefits.quickAndEasy.title")}</h3>
                  <p className="font-light text-base">{t("benefits.quickAndEasy.description")}</p>
                </div>
              </div>

              {/* Absolutely stress-free benefit */}
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-12 h-12 bg-[#1E3A5F] rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32" fill="none">
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M27.6618 12.5907C27.4244 12.0565 27.4244 11.4483 27.6618 10.9141C28.0056 10.141 27.8438 9.24173 27.2509 8.63036C26.8411 8.20783 26.6292 7.63715 26.6654 7.05499C26.7187 6.21306 26.2524 5.42224 25.4828 5.04666C24.9495 4.78688 24.5514 4.32157 24.3833 3.76237C24.1395 2.95382 23.4263 2.36645 22.5716 2.27151C21.9808 2.20578 21.4432 1.90218 21.0898 1.43374C20.5789 0.755603 19.7029 0.442616 18.8673 0.640842C18.2893 0.777513 17.6783 0.67214 17.1822 0.349763C16.4659 -0.116588 15.5334 -0.116588 14.817 0.349763C14.321 0.67214 13.7111 0.777513 13.1331 0.640842C12.2964 0.44366 11.4214 0.755603 10.9105 1.43374C10.5571 1.90322 10.0206 2.20682 9.42876 2.27151C8.574 2.36645 7.85976 2.95277 7.61706 3.76237C7.44888 4.32157 7.05077 4.78688 6.51748 5.04666C5.74682 5.42224 5.28165 6.21306 5.33488 7.05499C5.37213 7.63715 5.16031 8.20783 4.74943 8.63036C4.15653 9.24173 3.99473 10.1421 4.33855 10.9141C4.57592 11.4483 4.57592 12.0565 4.33855 12.5907C3.99473 13.3638 4.15653 14.2631 4.74943 14.8745C5.15924 15.297 5.37107 15.8677 5.33488 16.4498C5.28165 17.2918 5.74788 18.0826 6.51748 18.4582C7.05077 18.7179 7.44888 19.1833 7.61706 19.7425C7.86082 20.551 8.574 21.1384 9.42876 21.2333C10.0195 21.299 10.5571 21.6026 10.9105 22.0721C11.4214 22.7503 12.2975 23.0633 13.1331 22.865C13.7111 22.7284 14.322 22.8337 14.817 23.1561C15.5334 23.6225 16.4659 23.6225 17.1822 23.1561C17.6783 22.8337 18.2882 22.7284 18.8673 22.865C19.7039 23.0622 20.5789 22.7503 21.0898 22.0721C21.4432 21.6026 21.9797 21.299 22.5716 21.2333C23.4263 21.1384 24.1406 20.5521 24.3833 19.7425C24.5514 19.1833 24.9495 18.7179 25.4828 18.4582C26.2535 18.0826 26.7187 17.2918 26.6654 16.4498C26.6282 15.8677 26.84 15.297 27.2509 14.8745C27.8438 14.2631 28.0056 13.3638 27.6618 12.5907ZM22.6961 10.4384L19.9232 13.0873L20.5778 16.8285C20.6885 17.4629 20.0094 17.947 19.4282 17.6475L16.0007 15.8812L12.5732 17.6475C11.992 17.947 11.3128 17.4629 11.4235 16.8285L12.0782 13.0873L9.30528 10.4384C8.8348 9.98873 9.09452 9.20626 9.7449 9.11341L13.5769 8.56777L15.2907 5.16455C15.5813 4.58761 16.4211 4.58761 16.7117 5.16455L18.4255 8.56777L22.2575 9.11341C22.9079 9.20626 23.1666 9.98873 22.6972 10.4384H22.6961Z"
                      fill="#A9C8FF"
                    />
                    <path
                      d="M13.3823 23.8788C12.1273 24.1751 10.8213 23.7098 10.0538 22.6915C9.8771 22.4568 9.60566 22.3034 9.30974 22.27C8.7392 22.2074 8.21336 21.9988 7.77161 21.6806L4.36323 28.7624C4.18547 29.1318 4.48777 29.5501 4.90291 29.5073L7.72052 29.2184C7.9185 29.1985 8.11224 29.2872 8.22187 29.45L9.78662 31.765C10.0165 32.1062 10.5392 32.0686 10.717 31.6993L14.3691 24.1094L14.3517 24.0993C14.3104 24.0755 14.2687 24.0514 14.2286 24.0249C13.9806 23.8631 13.6719 23.8099 13.3823 23.8788Z"
                      fill="#A9C8FF"
                    />
                    <path
                      d="M18.6185 23.8783C19.8735 24.1746 21.1796 23.7093 21.947 22.691L21.9481 22.69C22.1248 22.4553 22.3962 22.3009 22.6921 22.2685C23.2616 22.2059 23.7885 21.9973 24.2303 21.6791L27.6376 28.762C27.8154 29.1313 27.5131 29.5496 27.0979 29.5069L24.2803 29.2179C24.0823 29.197 23.8886 29.2867 23.7789 29.4495L22.2142 31.7645C21.9832 32.1057 21.4616 32.0681 21.2839 31.6988L17.6317 24.1089C17.6473 24.0995 17.6631 24.0903 17.6788 24.0812C17.7103 24.0629 17.7417 24.0445 17.7722 24.0244C18.0203 23.8627 18.329 23.8095 18.6185 23.8783Z"
                      fill="#A9C8FF"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-base">{t("benefits.stressFree.title")}</h3>
                  <p className="font-light text-base">{t("benefits.stressFree.description")}</p>
                </div>
              </div>
            </div>
          </ShopBanner>
        </div>
      </div>
      <LoginModal key={email} defaultEmail={email} open={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />
      <JourneyAccountVerificationModal
        type={verificationMode}
        resendTokenType={TypeResendToken.CREATE_ACCOUNT}
        email={email!}
      />
    </>
  );
}
