"use client";

import { Icons } from "@/components/ui/icons";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";

interface JourneySelectCountriesSubmitProps {
  disabled: boolean;
  onNext: () => void;
  onBack?: (() => void) | null;
}
export function JourneySelectCountriesSubmit(props: JourneySelectCountriesSubmitProps) {
  const { disabled, onNext, onBack } = props;
  const t = useTranslations("shop.longJourney.selectCountries.submit");
  return (
    <div className="space-y-8 md:space-y-4">
      <div className={`flex items-center ${onBack ? "gap-6" : ""}`}>
        {onBack ? (
          <button onClick={onBack} type="button" className="flex-shrink-0 hover:opacity-50">
            <Icons.circleOutlineChevronleft className="size-14 " />
          </button>
        ) : null}
        <Button
          color={"yellow"}
          variant="filled"
          size="medium"
          disabled={disabled}
          onClick={onNext}
          trailingIcon={<East />}
          className="flex-1"
        >
          {t("label")}
        </Button>
      </div>
      {!onBack ? <p className="text-primary">{t("description")}</p> : null}
    </div>
  );
}
