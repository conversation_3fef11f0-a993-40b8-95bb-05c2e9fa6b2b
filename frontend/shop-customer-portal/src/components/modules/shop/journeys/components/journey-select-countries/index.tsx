"use client";

import { CountryInput } from "@/components/_common/forms/country-input";
import { JourneySelectCountriesItem } from "./journey-select-countries-item";
import { BaseCountry, Country } from "@/lib/api/country/types";
import { useCallback, useMemo } from "react";

interface JourneySelectCountriesProps {
  title: string;
  onSelect: (country: Country) => void;
  onRemove: (country: Country) => void;
  selected: Country[];
  countries: Country[];
  enableInput?: boolean;
}

export function JourneySelectCountries(props: JourneySelectCountriesProps) {
  const { title, onSelect, onRemove, selected, countries, enableInput = true } = props;
  const onSelectCountry = useCallback(
    (country: BaseCountry) => {
      const selected = countries.find((c) => c.code === country.code);
      if (!selected) {
        return;
      }
      onSelect(selected);
    },
    [countries, onSelect]
  );

  const availableCountries = useMemo(() => {
    const codeSet = new Set(selected.map((c) => c.code));
    return countries.filter((c) => !codeSet.has(c.code));
  }, [selected, countries]);

  const contractedCountryCodes = useMemo(() => {
    return selected.map((c) => c.code);
  }, [selected]);
  return (
    <div className="bg-surface-02 py-6 md:py-10 px-4 md:px-6 rounded-[32px] flex flex-col gap-6">
      <div className="hidden md:block">
        <p className="text-grey-blue font-bold text-2xl">{title}</p>
      </div>
      {enableInput ? (
        <div className="w-full">
          <CountryInput
            countries={availableCountries}
            onSelectCountry={onSelectCountry}
            recommendationEnabled
            contractedCountryCodes={contractedCountryCodes}
          />
        </div>
      ) : null}
      {selected.length > 0 ? (
        <div className="space-y-6">
          {selected.map((country) => (
            <JourneySelectCountriesItem key={country.id} country={country} onRemove={onRemove} />
          ))}
        </div>
      ) : null}
    </div>
  );
}
