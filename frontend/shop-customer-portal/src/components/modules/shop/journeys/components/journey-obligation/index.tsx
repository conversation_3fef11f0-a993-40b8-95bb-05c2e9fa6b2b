"use client";

import { Link } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import Image from "next/image";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useObligationCompletion } from "@/hooks/use-obligation-completion";
import { ObligationCheckForm } from "./obligation-check-form";
import { ObligationSidebar } from "./obligation-sidebar";
import { ShopCountryTabs } from "@/components/modules/shop/components/shop-tabs";
import { Error, Delete } from "@arthursenno/lizenzero-ui-react/Icon";

export function JourneyObligation() {
  const t = useTranslations("shop.longJourney.obligations");
  const { shoppingCart, setLocalCart, clearCountryObligation } = useShoppingCart();
  const { getCompletionStatus, markCountryComplete, isCountryComplete } = useObligationCompletion();
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);

  // Get sales countries from country selection step
  const salesCountries = shoppingCart.country.salesCountries || [];

  // Convert sales countries to license item format for compatibility
  const allCountries = salesCountries.map((country) => ({
    country_code: country.code,
    country_name: country.name,
    country_flag: country.flag_url,
    service_type: "EU_LICENSE" as const,
  }));

  // Set the first country as selected by default when component mounts
  useEffect(() => {
    if (allCountries.length > 0 && !selectedCountry) {
      setSelectedCountry(allCountries[0].country_code);
    }
  }, [allCountries, selectedCountry]);

  const handleCountrySelect = (countryCode: string) => {
    setSelectedCountry(countryCode);
  };

  const handleCountryComplete = (countryCode: string) => {
    markCountryComplete(countryCode);
  };

  const handleDeleteSelectedCountry = () => {
    if (!selectedCountry) return;

    // Determine next country before updating state
    const currentIndex = allCountries.findIndex((item) => item.country_code === selectedCountry);
    const remaining = allCountries.filter((item) => item.country_code !== selectedCountry);
    const nextCode = remaining.length ? remaining[Math.min(currentIndex, remaining.length - 1)].country_code : null;

    // Remove from local cart sales countries
    setLocalCart((cart) => ({
      ...cart,
      country: {
        ...cart.country,
        salesCountries: (cart.country.salesCountries || []).filter((c) => c.code !== selectedCountry),
      },
    }));

    // Clear obligation progress for this country
    clearCountryObligation(selectedCountry);

    // Update selected tab
    setSelectedCountry(nextCode);
  };

  const selectedCountryData = selectedCountry
    ? allCountries.find((item) => item.country_code === selectedCountry) || {
        country_code: selectedCountry,
        country_name: selectedCountry,
        country_flag: null,
      }
    : null;

  // Get completion status for the selected country using existing function
  const isSelectedCountryComplete = selectedCountry ? isCountryComplete(selectedCountry) : false;

  // Check if all countries are complete
  const areAllCountriesComplete = allCountries.every((country) => isCountryComplete(country.country_code));

  return (
    <>
      {/* Country Tabs */}
      <ShopCountryTabs
        selectedCountry={selectedCountry}
        onCountrySelect={handleCountrySelect}
        obligationCompletionStatus={getCompletionStatus()}
        countries={allCountries}
      />
      <div className="h-px w-full bg-tonal-dark-cream-80 my-6" />

      {/* Main Content */}
      <div className="flex md:flex-row flex-col gap-6 w-full mb-20">
        {/* Left Column - Info Banner + Form */}
        <div className="flex-1 space-y-6">
          {/* Info Banner */}
          <div className="bg-[#F0F0EF] rounded-[20px] p-6 flex items-start gap-4">
            <div className="p-2 flex-shrink-0">
              <Error className="fill-primary size-6" />
            </div>
            <p className=" text-primary">{t("infoBanner.text")}</p>
          </div>

          {/* Country Status Button */}
          {selectedCountryData && (
            <div className="flex items-center gap-4 p-4 w-fit">
              {/* Delete Country */}
              <Button variant="text" size="iconSmall" color="gray" onClick={handleDeleteSelectedCountry} type="button">
                <Delete className="size-6 fill-primary" />
              </Button>
              <div className="flex items-center gap-4">
                {/* Country Flag */}
                <div className="w-10 h-10 rounded-full overflow-hidden flex items-center justify-center">
                  {selectedCountryData.country_flag ? (
                    <Image
                      src={selectedCountryData.country_flag}
                      alt={`${selectedCountryData.country_name} flag`}
                      width={32}
                      height={24}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-300 rounded flex items-center justify-center text-xs text-gray-600">
                      {selectedCountryData.country_code}
                    </div>
                  )}
                </div>

                {/* Country Name */}
                <span className="text-primary font-bold text-[28px]">{selectedCountryData.country_name}</span>
              </div>

              {/* Status Badge */}
              <div
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  isSelectedCountryComplete ? "bg-light-green text-dark-green" : "bg-light-yellow text-dark-yellow"
                }`}
              >
                {isSelectedCountryComplete ? t("status.complete") : t("status.open")}
              </div>
            </div>
          )}

          {/* Obligation Check Form */}
          {selectedCountryData ? (
            <ObligationCheckForm
              key={selectedCountryData.country_code} // Force re-render when country changes
              countryCode={selectedCountryData.country_code}
              countryName={selectedCountryData.country_name || selectedCountryData.country_code}
              countryFlag={selectedCountryData.country_flag || undefined}
              onComplete={handleCountryComplete}
              isLastCountry={(() => {
                const currentIndex = allCountries.findIndex((item) => item.country_code === selectedCountry);
                return currentIndex === allCountries.length - 1;
              })()}
            />
          ) : (
            <div className="flex flex-col gap-4 md:gap-6 bg-surface-02 p-4 py-6 md:p-8 rounded-4xl">
              <p className="text-center text-tonal-dark-cream-30">{t("selectCountryMessage")}</p>
            </div>
          )}
        </div>

        {/* Right Sidebar */}
        <ObligationSidebar areAllCountriesComplete={areAllCountriesComplete} />
      </div>
    </>
  );
}
