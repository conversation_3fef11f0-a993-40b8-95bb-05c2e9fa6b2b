"use client";

import { Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CountryIcon } from "@/components/_common/country-icon";
import { Country } from "@/lib/api/country/types";
interface JourneySelectCountriesItemProps {
  country: Country;
  onRemove: (country: Country) => void;
}

export function JourneySelectCountriesItem({ country, onRemove }: JourneySelectCountriesItemProps) {
  async function handleRemoveItem() {
    onRemove(country);
  }

  return (
    <div className="group rounded-4xl bg-background overflow-hidden open:shadow-elevation-02-1">
      <div className="flex items-center justify-between py-5 px-4 md:px-7">
        <div className="flex items-center gap-4">
          <Button variant="text" size="iconSmall" color="gray" onClick={handleRemoveItem} type="button">
            <Delete className="size-5 fill-primary" />
          </Button>
          <CountryIcon country={{ name: country.name, flag_url: country.flag_url }} className="size-6 md:size-8" />
          <p className="text-xl md:text-2xl font-bold text-primary">{country.name}</p>
        </div>
      </div>
    </div>
  );
}
