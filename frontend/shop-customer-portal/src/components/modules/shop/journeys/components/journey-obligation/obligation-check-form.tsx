"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { KeyboardArrowRight, KeyboardArrowLeft, Check } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { CountryIcon } from "@/components/_common/country-icon";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { OptionCard } from "@/components/ui/option-card";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useObligationCompletion } from "@/hooks/use-obligation-completion";
import {
  getObligationCheckSectionsByCountryCode,
  getCriteriasBySectionId,
  ObligationCriteria,
  submitObligationCheckAnswers,
  ObligationCheckAnswer,
} from "@/lib/api/obligation-check";
import { Icons } from "@/components/ui/icons";

interface ObligationCheckFormProps {
  countryCode: string;
  countryName: string;
  countryFlag?: string;
  onComplete: (countryCode: string) => void;
  isLastCountry?: boolean;
}

interface QuestionAnswer {
  criteriaId: number;
  answer: "YES" | "NO" | null;
}

export function ObligationCheckForm({
  countryCode,
  countryName,
  countryFlag,
  onComplete,
  isLastCountry = false,
}: ObligationCheckFormProps) {
  const t = useTranslations("shop.longJourney.obligations.form");
  const { getCountryObligation, updateCountryObligation } = useShoppingCart();
  const { isCountryComplete: checkCountryComplete } = useObligationCompletion();

  // Load saved data from local storage with proper synchronization
  const [currentSectionIndex, setCurrentSectionIndex] = useState(() => {
    return getCountryObligation(countryCode).currentSectionIndex;
  });
  const [answers, setAnswers] = useState<Record<number, QuestionAnswer>>(() => {
    return getCountryObligation(countryCode).answers;
  });

  // Sync state with storage when country changes
  useEffect(() => {
    const savedData = getCountryObligation(countryCode);
    setCurrentSectionIndex(savedData.currentSectionIndex);
    setAnswers(savedData.answers);
  }, [countryCode, getCountryObligation]);

  // Fetch sections for the country
  const { data: sections } = useQuery({
    queryKey: ["obligation-check-sections", countryCode],
    queryFn: () => getObligationCheckSectionsByCountryCode(countryCode),
    enabled: !!countryCode,
  });

  // Fetch criteria for current section
  const currentSection = sections?.[currentSectionIndex];
  const { data: criteria, isLoading } = useQuery({
    queryKey: ["obligation-criteria", currentSection?.id],
    queryFn: () => {
      if (!currentSection) {
        return Promise.resolve([]);
      }
      return getCriteriasBySectionId(currentSection.id);
    },
    enabled: !!currentSection,
  });

  // Initialize answers when criteria loads
  useEffect(() => {
    if (criteria) {
      setAnswers((prev) => {
        const newAnswers = { ...prev };
        criteria.forEach((criterion) => {
          if (!newAnswers[criterion.id]) {
            newAnswers[criterion.id] = {
              criteriaId: criterion.id,
              answer: null,
            };
          }
        });
        return newAnswers;
      });
    }
  }, [criteria]);

  // Get visible questions based on conditional logic
  const getVisibleQuestions = () => {
    if (!criteria) return [];

    const visibleQuestions: ObligationCriteria[] = [];

    for (const criterion of criteria) {
      // Always show the first question
      if (visibleQuestions.length === 0) {
        visibleQuestions.push(criterion);
        continue;
      }

      // Check if this question should be shown based on previous answers
      let shouldShow = false;

      // Look for any previous question that has conditional logic pointing to this one
      for (const prevCriterion of criteria) {
        const prevAnswer = answers[prevCriterion.id]?.answer;
        if (!prevAnswer) continue;

        const relevantOption = prevCriterion.options.find((opt: any) => opt.option_value === prevAnswer);
        if (relevantOption?.value === "CONDITIONAL" && relevantOption.conditional_criteria_id === criterion.id) {
          shouldShow = true;
          break;
        }
      }

      // If no conditional logic found, show the question (fallback for non-conditional questions)
      if (
        !shouldShow &&
        !criteria.some((c: any) => c.options.some((opt: any) => opt.conditional_criteria_id === criterion.id))
      ) {
        shouldShow = true;
      }

      if (shouldShow) {
        visibleQuestions.push(criterion);
      }
    }

    return visibleQuestions;
  };

  const visibleQuestions = getVisibleQuestions();

  const handleAnswerChange = useCallback(
    (criteriaId: number, answer: "YES" | "NO") => {
      setAnswers((prevAnswers) => {
        const newAnswers = {
          ...prevAnswers,
          [criteriaId]: {
            criteriaId,
            answer,
          },
        };

        // Save to local storage after state update
        updateCountryObligation(countryCode, {
          answers: newAnswers,
        });

        return newAnswers;
      });
    },
    [countryCode, updateCountryObligation]
  );

  // Save section changes to local storage with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateCountryObligation(countryCode, {
        currentSectionIndex,
      });
    }, 100); // Small delay to prevent rapid updates

    return () => clearTimeout(timeoutId);
  }, [countryCode, currentSectionIndex, updateCountryObligation]);

  const isCurrentSectionComplete = () => {
    if (!visibleQuestions.length) return false;
    return visibleQuestions.every(
      (criterion) => answers[criterion.id]?.answer !== null && answers[criterion.id]?.answer !== undefined
    );
  };

  // Check if all sections are complete for the country
  const isCountryComplete = () => {
    if (!sections || sections.length === 0) return false;

    // Must be on the last section
    if (currentSectionIndex !== sections.length - 1) return false;

    // Current (last) section must be complete
    return isCurrentSectionComplete();
  };

  const handleNextSection = async () => {
    if (!currentSection) return;

    // Prepare answers for the current section
    const sectionAnswers: ObligationCheckAnswer[] = visibleQuestions
      .filter((criterion) => answers[criterion.id]?.answer)
      .map((criterion) => ({
        criteriaId: criterion.id,
        answer: answers[criterion.id].answer as "YES" | "NO",
      }));

    try {
      // Submit answers for the current section
      await submitObligationCheckAnswers({
        countryCode,
        sectionId: currentSection.id,
        answers: sectionAnswers,
      });

      if (sections && currentSectionIndex < sections.length - 1) {
        // Move to next section
        setCurrentSectionIndex((prev) => prev + 1);
      } else {
        // Last section completed - mark country as complete but don't move to next country
        if (isCountryComplete()) {
          updateCountryObligation(countryCode, {
            completed: true,
          });
        }
        onComplete(countryCode);
        // Remove automatic navigation - user will manually select next country via tabs
      }
    } catch (error) {
      // Still allow progression for now, but could show error message
      if (sections && currentSectionIndex < sections.length - 1) {
        setCurrentSectionIndex((prev) => prev + 1);
      } else {
        // Check if country is truly complete before marking (even with submission error)
        if (isCountryComplete()) {
          updateCountryObligation(countryCode, {
            completed: true,
          });
        }
        onComplete(countryCode);
        // Remove automatic navigation even with submission error - user will manually select next country
      }
    }
  };

  const isLastSection = sections ? currentSectionIndex === sections.length - 1 : true;
  const isFirstSection = currentSectionIndex === 0;

  const handlePreviousSection = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex((prev) => prev - 1);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 md:gap-6 flex-1 bg-surface-02 p-4 py-6 md:p-8 rounded-4xl">
        <div className="animate-pulse space-y-6">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  // Handle case when no sections are configured for this country
  if (!sections || sections.length === 0) {
    return (
      <div className="flex flex-col gap-4 md:gap-6 flex-1 bg-surface-02 p-4 py-6 md:p-8 rounded-4xl">
        <div className="text-center py-12">
          <div className="mb-4">
            <div className="bg-tonal-dark-cream-80 rounded-full p-4 w-16 h-16 mx-auto flex items-center justify-center">
              <span className="text-2xl">📋</span>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-primary mb-2">{t("noObligationCheck.title")}</h3>
          <p className="text-tonal-dark-cream-30 text-base">{t("noObligationCheck.description", { countryName })}</p>
          <div className="mt-6">
            <Button
              onClick={() => {
                // Mark as complete since there are no questions
                updateCountryObligation(countryCode, { completed: true });
                onComplete(countryCode);
                // Don't auto-navigate - user will manually select next country via tabs
              }}
              variant="filled"
              color="yellow"
              size="medium"
              trailingIcon={<KeyboardArrowRight />}
            >
              {t("buttons.complete")}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Handle case when no criteria are configured for current section
  if (currentSection && (!criteria || criteria.length === 0)) {
    return (
      <div className="flex flex-col gap-4 md:gap-6 flex-1 bg-surface-02 p-4 py-6 md:p-8 rounded-4xl">
        <div className="text-center py-12">
          <div className="mb-4">
            <div className="bg-tonal-dark-cream-80 rounded-full p-4 w-16 h-16 mx-auto flex items-center justify-center">
              <span className="text-2xl">❓</span>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-primary mb-2">{t("noQuestions.title")}</h3>
          <p className="text-tonal-dark-cream-30 text-base">{t("noQuestions.description")}</p>
          <div className="mt-6">
            <Button
              onClick={() => {
                // Mark as complete since there are no questions
                updateCountryObligation(countryCode, { completed: true });
                onComplete(countryCode);
                // Don't auto-navigate - user will manually select next country via tabs
              }}
              variant="filled"
              color="yellow"
              size="medium"
              trailingIcon={<KeyboardArrowRight />}
            >
              {t("buttons.complete")}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-8 flex-1 bg-surface-02 p-6 md:p-10 rounded-4xl">
      {/* Questions */}
      {visibleQuestions.length > 0 && (
        <div className="space-y-12">
          {visibleQuestions.map((criterion) => (
            <div key={criterion.id} className="space-y-6">
              <div className="flex items-center gap-3">
                <h3 className="text-xl font-bold text-primary leading-tight">{criterion.title}</h3>
                {criterion.help_text && (
                  <QuestionTooltip className="size-5">
                    <QuestionTooltipDescription>{criterion.help_text}</QuestionTooltipDescription>
                  </QuestionTooltip>
                )}
              </div>

              {/* YES/NO Radio Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <OptionCard
                  label={t("options.yes")}
                  value="YES"
                  name={`question-${criterion.id}`}
                  checked={answers[criterion.id]?.answer === "YES"}
                  onChange={(value) => handleAnswerChange(criterion.id, value)}
                />

                <OptionCard
                  label={t("options.no")}
                  value="NO"
                  name={`question-${criterion.id}`}
                  checked={answers[criterion.id]?.answer === "NO"}
                  onChange={(value) => handleAnswerChange(criterion.id, value)}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="border-t border-tonal-dark-cream-80 pt-8">
        <div className="flex items-center justify-between">
          <div className="text-tonal-dark-cream-30">
            {t("footer.sectionProgress", {
              current: currentSectionIndex + 1,
              total: sections?.length || 1,
            })}
          </div>

          <div className="flex items-center gap-4">
            {/* Back Button */}
            {!isFirstSection && (
              <button onClick={handlePreviousSection} type="button" className="flex-shrink-0 hover:opacity-50">
                <Icons.circleOutlineChevronleft className="size-14 " />
              </button>
            )}

            {/* Continue/Complete Button */}
            <Button
              onClick={handleNextSection}
              variant="filled"
              color="dark-blue"
              size="large"
              trailingIcon={
                !isLastSection ? (
                  <KeyboardArrowRight />
                ) : checkCountryComplete(countryCode) ? (
                  <Check className="size-5 fill-dark-green" />
                ) : undefined
              }
              className={`px-8 py-4 gap-3 rounded-full font-semibold ${
                isLastSection && checkCountryComplete(countryCode)
                  ? "!bg-light-green !text-dark-green hover:!bg-light-green"
                  : ""
              }`}
              disabled={!isCurrentSectionComplete()}
            >
              {isLastSection ? t("buttons.complete") : t("buttons.continue")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
