"use client";

import { MapCountries } from "@/components/_common/map";
import { Country } from "@/lib/api/country/types";
import { useMemo } from "react";

interface JourneySelectCountriesMapProps {
  countries: Country[];
  selected: Country[];
  onSelect: (country: Country) => void;
}

export function JourneySelectCountriesMap(props: JourneySelectCountriesMapProps) {
  const { countries, selected, onSelect } = props;

  async function handleSelectCountry(countryCode: string) {
    const country = countries.find((c) => c.code === countryCode);
    if (!country) {
      return;
    }
    onSelect(country);
  }
  const selectedCountries = useMemo(() => {
    return selected.map((c) => c.code);
  }, [selected]);
  return (
    <MapCountries
      className="hidden md:block aspect-square h-auto"
      onSelectCountry={handleSelectCountry}
      selectedCountries={selectedCountries}
      liberatedCountries={countries}
      contractedCountryCodes={selectedCountries}
    >
      <div className="absolute top-0 mt-3 ml-3"></div>
    </MapCountries>
  );
}
