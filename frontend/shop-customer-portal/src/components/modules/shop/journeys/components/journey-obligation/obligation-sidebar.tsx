"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { Icons } from "@/components/ui/icons";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { useTranslations } from "next-intl";

interface ObligationSidebarProps {
  areAllCountriesComplete: boolean;
}

export function ObligationSidebar({ areAllCountriesComplete }: ObligationSidebarProps) {
  const t = useTranslations("shop.longJourney.obligations");

  return (
    <div className="flex flex-col gap-6 md:max-w-[40%]">
      <ShopBanner title="" style={{ width: "100%" }} className="bg-light-blue">
        <div className="space-y-8">
          {/* Small and easy steps */}
          <div className="flex items-start gap-4">
            <IconBanner
              className="text-white flex-shrink-0"
              icon={() => <Lightbulb className="fill-tonal-dark-blue-80 size-10" />}
            />
            <div>
              <p className="font-bold text-base text-primary mb-2">{t("sidebar.steps.title")}</p>
              <span className="text-sm text-primary">{t("sidebar.steps.description")}</span>
            </div>
          </div>

          {/* Absolutely stress free */}
          <div className="flex items-start gap-4">
            <IconBanner
              className="text-white flex-shrink-0"
              icon={() => <Icons.awardBadge className="fill-tonal-dark-blue-80 size-10" />}
            />
            <div>
              <p className="font-bold text-base text-primary mb-2">{t("sidebar.stressFree.title")}</p>
              <span className="text-sm text-primary">{t("sidebar.stressFree.description")}</span>
            </div>
          </div>
        </div>
      </ShopBanner>

      {/* View Results Button - Full Width */}
      <Button
        variant="filled"
        color="yellow"
        size="medium"
        trailingIcon={<East />}
        disabled={!areAllCountriesComplete}
        style={{ width: "100%" }}
      >
        {t("buttons.viewResults")}
      </Button>
    </div>
  );
}
