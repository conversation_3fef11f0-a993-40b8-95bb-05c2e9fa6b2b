"use client";
import { Stepper } from "@/components/ui/stepper";
import { useTranslations } from "next-intl";

interface ShopStepperProps {
  step: number;
}

type LongJourneyStepId =
  | "select-countries"
  | "check-obligations"
  | "see-results"
  | "choose-epr-services"
  | "estimate-your-costs"
  | "enter-information"
  | "review-order";

interface ShopStepperByIdProps {
  stepById: LongJourneyStepId;
}

// TODO: refactor this and add to layout
export function ShopLicenseStepper({ step }: ShopStepperProps) {
  const t = useTranslations("shop.common.stepper.license");
  return (
    <Stepper
      className="max-w-7xl mx-auto pt-8 md:pt-12"
      currentStep={step}
      steps={[
        { index: 1, title: t("volumeCalculator") },
        { index: 2, title: t("data") },
        { index: 3, title: t("payment") },
        { index: 4, title: t("checkAndOrder") },
      ]}
    />
  );
}

export function ShopActionStepper({ step }: ShopStepperProps) {
  const t = useTranslations("shop.common.stepper.actionGuide");
  return (
    <Stepper
      className="max-w-7xl mx-auto pt-8 md:pt-12"
      currentStep={step}
      steps={[
        { index: 1, title: t("shoppingCart") },
        { index: 2, title: t("data") },
        { index: 3, title: t("payment") },
        { index: 4, title: t("checkAndOrder") },
      ]}
    />
  );
}

/**
 * A stepper component for the long journey shop flow that displays progress through multiple steps.
 *
 * @param props - The component props
 * @param props.stepById - The ID of the current step to highlight in the stepper
 * @returns A Stepper component configured with the long journey steps and current step position
 *
 * @example
 * ```tsx
 * <ShopLongJourneyStepper stepById="select-countries" />
 * ```
 */
export function ShopLongJourneyStepper({ stepById }: ShopStepperByIdProps) {
  const t = useTranslations("shop.common.stepper.longJourney");

  const steps = [
    { index: 1, title: t("selectCountries"), id: "select-countries" },
    { index: 2, title: t("checkObligations"), id: "check-obligations" },
    { index: 3, title: t("seeResults"), id: "see-results" },
    { index: 4, title: t("chooseEprServices"), id: "choose-epr-services" },
    { index: 5, title: t("estimateYourCosts"), id: "estimate-your-costs" },
    { index: 6, title: t("enterInformation"), id: "enter-information" },
    { index: 7, title: t("reviewOrder"), id: "review-order" },
  ];

  // Find the current step index based on stepById
  const currentStep = steps.find((step) => step.id === stepById)?.index || 1;

  return (
    <Stepper
      className="w-full max-w-7xl mx-auto pt-8 md:pt-12 px-4 sm:px-6 lg:px-8"
      currentStep={currentStep}
      steps={steps}
    />
  );
}
