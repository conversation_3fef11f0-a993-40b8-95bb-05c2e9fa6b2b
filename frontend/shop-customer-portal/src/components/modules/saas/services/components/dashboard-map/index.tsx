"use client";

import { Launch } from "@arthursenno/lizenzero-ui-react/Icon";
import * as MapLib from "mapbox-gl";
import { useState } from "react";
import Map, { Layer, LayerProps, MapStyle, Source } from "react-map-gl";
import { Divider } from "@/components/_common/divider";
import { CountryIcon } from "@/components/_common/country-icon";
import Link from "next/link";
import countriesPolygon from "./countries.json";
import { CountryStatus, MapCountryStatus } from "./country-status";
import mapStyle from "./map-style.json";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { BaseCountry } from "@/lib/api/country/types";

export const ORDER_FILTERS = [
  { label: "Last modified", value: "LAST_MODIFIED" },
  { label: "First modified", value: "FIRST_MODIFIED" },
  { label: "A-Z", value: "ASC" },
  { label: "Z-A", value: "DESC" },
] as const;

export const STATUS_FILTERS = [
  { label: "All status", value: "ALL" },
  { label: "Open to-dos", value: "OPEN_TO_DOS", color: "red" },
  { label: "In Review", value: "IN_REVIEW", color: "yellow" },
  { label: "All done", value: "DONE", color: "green" },
] as const;

export const STATUS_STYLE = {
  OPEN_TO_DOS: {
    bgClass: "bg-tonal-red-90",
    textClass: "text-error",
    bgColor: "#FCEAE8",
    textColor: "#CD2C19",
    fillClass: "fill-error",
    text: "Open to-dos",
    mapInfo: {
      bgFocusMap: "#E64330",
      bgOutFocusMap: "#F3A198",
      textClass: "text-error",
      fillClass: "fill-error",
    },
    myCountries: {
      textClass: "text-tonal-red-50",
      fillClass: "fill-tonal-red-50",
    },
  },
  IN_REVIEW: {
    bgClass: "bg-alert-container",
    bgColor: "#FFF5CC",
    textColor: "#997C00",
    textClass: "text-on-alert-container",
    fillClass: "fill-on-alert-container",
    text: "Pending",
    mapInfo: {
      bgFocusMap: "#CCA500",
      bgOutFocusMap: "#E6D280",
      textClass: "text-alert",
      fillClass: "fill-alert",
    },
    myCountries: {
      textClass: "text-on-alert-container",
      fillClass: "fill-on-alert-container",
    },
  },
  DONE: {
    bgClass: "bg-success-container",
    bgColor: "#D8F2D8",
    textColor: "#1B6C64",
    textClass: "text-tonal-dark-green-30",
    fillClass: "fill-tonal-dark-green-30",
    text: "Done",
    mapInfo: {
      bgFocusMap: "#1B6C64",
      bgOutFocusMap: "#8DB6B2",
      textClass: "text-tonal-dark-green-30",
      fillClass: "fill-tonal-dark-green-30",
    },
    myCountries: {
      textClass: "text-success",
      fillClass: "fill-success",
    },
  },
} as const;

interface CountryInfo {
  country: BaseCountry;
  statusDetails: string[];
  status: "OPEN_TO_DOS" | "IN_REVIEW" | "DONE";
  x: number;
  y: number;
}

interface DashboardMapProps {
  children?: React.ReactNode;
  type: "EU_LICENSE" | "ACTION_GUIDE";
  countries: (BaseCountry & {
    status: MapCountryStatus;
    statusDetails?: string[];
  })[];
  className?: string;
}

export function DashboardMap({ children, countries, type, className }: DashboardMapProps) {
  const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

  if (!mapboxToken && process.env.NODE_ENV !== "production") {
    console.warn("⚠️  WARNING: NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN is not set. Map functionality will be disabled.");
  }

  const [hoverInfo, setHoverInfo] = useState<CountryInfo | null>(null);
  const [clickedInfo, setClickedInfo] = useState<CountryInfo | null>(null);

  const info = hoverInfo || clickedInfo;

  function handleHover(event: MapLib.MapLayerMouseEvent) {
    if (clickedInfo) return;

    const {
      features,
      point: { x, y },
    } = event;

    if (!features || !features[0]) {
      setHoverInfo(null);
      return;
    }

    const selectFeature = features[0];
    const countryName: string = selectFeature?.properties?.admin;

    const country = countries.find((country) => country.name === countryName);

    if (!country) {
      setHoverInfo(null);
      return;
    }

    setHoverInfo({
      country: {
        id: country.id,
        code: country.code,
        name: country.name,
        flag_url: country.flag_url,
      },
      status: country.status,
      statusDetails: [],
      x,
      y: y - 25,
    });
  }

  function handleClick(event: MapLib.MapLayerMouseEvent) {
    const {
      features,
      point: { x, y },
    } = event;

    // If clicking outside of a country, clear the clicked state
    if (!features || !features[0]) {
      setClickedInfo(null);
      return;
    }

    const selectFeature = features[0];
    const countryName: string = selectFeature?.properties?.admin;

    const country = countries.find((country) => country.name === countryName);

    if (!country) {
      setClickedInfo(null);
      return;
    }

    setClickedInfo({
      country: {
        id: country.id,
        code: country.code,
        name: country.name,
        flag_url: country.flag_url,
      },
      status: country.status,
      statusDetails: [],
      x,
      y: y - 25,
    });
  }

  if (!countries.length) {
    return (
      <div className="rounded-t-[40px] overflow-hidden w-full relative min-h-[400px] h-full">
        <Skeleton className="w-full h-full" />
        <div className="h-[300px] m-10">
          <Skeleton className="w-full h-full" />
        </div>
      </div>
    );
  }

  const polygonLayer: LayerProps = {
    id: "countries",
    type: "fill-extrusion",
    paint: {
      "fill-extrusion-color": [
        "match",
        ["get", "admin"],
        ...countries?.flatMap((country) => {
          const statusStyle = STATUS_STYLE[country.status];
          const color = statusStyle.mapInfo.bgFocusMap;

          return [country.name, color];
        }),
        "#D7D6D5",
      ],
      "fill-extrusion-opacity": 1,
    },
  };

  const lineLayer: LayerProps = {
    id: "countries-line-layer",
    type: "line",
    source: "countries",
    layout: {},
    paint: {
      "line-color": "#FFF",
      "line-width": ["case", ["==", ["get", "admin"], info?.country.name || null], 1.5, 0.5],
    },
  };

  return (
    <div
      className={cn("rounded-[40px] overflow-hidden bg-white w-full relative", className)}
      style={{
        cursor: hoverInfo ? "pointer" : "auto",
      }}
      id="dashboard-Map"
    >
      <div className="h-[500px]">
        <Map
          mapLib={MapLib as any}
          mapboxAccessToken={mapboxToken}
          mapStyle={mapStyle as MapStyle}
          initialViewState={{ latitude: 46.8182, longitude: 8.2275, zoom: 3 }}
          maxZoom={4}
          minZoom={3}
          pitch={0}
          bearing={0}
          doubleClickZoom={false}
          interactiveLayerIds={["countries"]}
          onMouseMove={handleHover as any}
          onClick={handleClick as any}
        >
          <Source type="geojson" data={countriesPolygon as GeoJSON.GeoJSON}>
            <Layer {...polygonLayer} />
            <Layer {...lineLayer} />
          </Source>
          {info && (
            <div
              className="md:block hidden absolute bg-background shadow-xl rounded-2xl p-5 z-50 w-72"
              style={{
                left: info.x,
                top: info.y,
                boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)",
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center gap-3 py-3">
                <CountryIcon country={info.country} className="size-8" />
                <div>
                  <Link
                    href={`./${type.toLocaleLowerCase().replace("_", "-")}/countries/${info.country.code}`}
                    className="text-support-blue text-base hover:underline font-bold flex items-center gap-1"
                  >
                    {info.country.name}
                    <Launch className="fill-support-blue size-4" />
                  </Link>
                  <p className="text-xs text-tonal-dark-cream-30">Your last entry: 06.05.2023</p>
                </div>
              </div>

              <Divider style={{ margin: 0 }} />

              <div className="py-3 space-y-2">
                <p className="text-primary font-bold text-sm">Status</p>
                <CountryStatus statusValue={info.status} />
              </div>
            </div>
          )}
          {children}
        </Map>
      </div>
      <div className="w-full m-10 space-y-3">
        <div className={cn("flex items-center gap-3 text-sm font-bold", STATUS_STYLE.DONE.mapInfo.textClass)}>
          <div className={cn("size-5 rounded-full bg-tonal-dark-green-30")} />
          Done
        </div>
        <div className={cn("flex items-center gap-3 text-sm font-bold", STATUS_STYLE.IN_REVIEW.mapInfo.textClass)}>
          <div className={cn("size-5 rounded-full bg-alert")} />
          In Review
        </div>
        <div className={cn("flex items-center gap-3 text-sm font-bold", STATUS_STYLE.OPEN_TO_DOS.mapInfo.textClass)}>
          <div className={cn("size-5 rounded-full bg-tonal-red-30")} />
          Open to-dos
        </div>
      </div>
    </div>
  );
}
