"use client";

import { EmailSchema } from "@/components/_common/forms/schemas";
import { Link, usePathname } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { SuccessResetPassword } from "./success-reset-password";
import { UserTypes } from "@/utils/user";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { AuthTitle } from "../components/auth-title";
import { postForgotPassword } from "@/lib/api/auth";

export function ForgotPasswordPage() {
  const [recover, setRecover] = useState(false);

  const pathname = usePathname();
  const role = pathname.includes("partner-hub") ? UserTypes.PARTNER : UserTypes.CUSTOMER;

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isLoading, isSubmitting },
  } = useForm<{
    email: string;
  }>({
    resolver: zodResolver(EmailSchema),
  });

  async function submit({ email }: { email: string }) {
    const domain = process.env.NEXT_PUBLIC_DOMAIN;

    if (!domain && process.env.NODE_ENV !== "production") {
      console.warn("⚠️  WARNING: NEXT_PUBLIC_DOMAIN is not set. Using relative URL for password recovery callback.");
    }

    const callbackUrl = domain ? `${domain}/auth/recover-password` : `/auth/recover-password`;

    try {
      const res = await postForgotPassword({
        email,
        callbackUrl,
      });

      if (res.data.statusCode !== 201 && res?.status !== 201) {
        setError("email", { message: res?.data.message });
        return;
      }
    } catch (error: any) {
      setError("email", { message: error });
      enqueueSnackbar(error, { variant: "error" });
      return;
    }

    setRecover(true);
  }

  const backUrl = role === UserTypes.CUSTOMER ? "/auth/login" : "/partner-hub/auth/login";

  const t = useTranslations("modules.auth.pages.forgotPassword");

  if (recover) return <SuccessResetPassword />;

  return (
    <>
      <AuthTitle title={t("title")} subtitle={t("subtitle")} />
      <form className="w-full" onSubmit={handleSubmit(submit)}>
        <div className="flex mb-10 ">
          <div className="w-full  mb-5">
            <div className="flex">
              <Input
                label={t("form.email.label")}
                placeholder={t("form.email.placeholder")}
                {...register("email")}
                type="email"
                variant={errors.email ? "error" : "enabled"}
                errorMessage={errors.email && errors.email.message}
                rightIcon={errors.email && <Error className="fill-error size-5" />}
              />
            </div>
          </div>
        </div>

        <Button
          color="yellow"
          size="medium"
          variant="filled"
          className="w-full"
          disabled={errors.email || isLoading || isSubmitting ? true : false}
        >
          {isLoading || isSubmitting ? t("form.submitButton.loading") : t("form.submitButton.label")}
        </Button>
      </form>
      <div className="mt-4 text-center w-full flex items-center justify-center">
        <Link href={backUrl}>
          <Button variant="text" size="small" color="light-blue">
            {t("goBackToLogin")}
          </Button>
        </Link>
      </div>
    </>
  );
}
