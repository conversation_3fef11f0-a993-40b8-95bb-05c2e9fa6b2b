"use client";

import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, type Stripe } from "@stripe/stripe-js";

const pk = process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY;
const stripePromise: Promise<Stripe | null> = pk ? loadStripe(pk) : Promise.resolve(null);

export function StripeProvider({ children }: { children: React.ReactNode }) {
  if (!pk && process.env.NODE_ENV !== "production") {
    console.warn("NEXT_PUBLIC_STRIPE_PUBLIC_KEY is not set. Stripe Elements is disabled.");
  }
  return <Elements stripe={stripePromise}>{children}</Elements>;
}
