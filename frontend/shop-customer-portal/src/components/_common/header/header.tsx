"use client";

import { CountryDropdown } from "@/components/_common/country-dropdown";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Liz<PERSON><PERSON>oLogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { useSession } from "next-auth/react";
import { Link, usePathname } from "@/i18n/navigation";
import { useCustomer } from "@/hooks/use-customer";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { FaUser } from "react-icons/fa6";
import { useMediaQuery } from "@/hooks/use-media-query";

const landingPageUrl = process.env.NEXT_PUBLIC_LANDING_PAGE_URL || "";

if (!landingPageUrl && process.env.NODE_ENV !== "production") {
  console.warn("⚠️  WARNING: NEXT_PUBLIC_LANDING_PAGE_URL is not set. Using empty string as fallback.");
}

interface HeaderComponentProps {
  localeList: { name: string; code: string; flag: string }[];
  children?: React.ReactNode;
}

export function HeaderComponent({ localeList, children }: HeaderComponentProps) {
  const t = useTranslations("shop.common.header");
  const session = useSession();
  const isMobile = useMediaQuery("(max-width: 1024px)");

  const { customer } = useCustomer();
  const router = useRouter();
  const pathname = usePathname();

  function handleGotToPage() {
    const homeUrl = customer?.hasActiveContract
      ? (() => {
          const contracts = customer.contracts;
          let goTo = `/saas/eu-license`;

          if (!contracts.find((contract) => contract.type === `EU_LICENSE`)) goTo = "/saas/direct-license";

          if (goTo.includes("direct-license") && !contracts.find((contract) => contract.type === `DIRECT_LICENSE`))
            goTo = "/saas/action-guide";

          return goTo;
        })()
      : landingPageUrl;

    router.push(homeUrl);
  }

  return (
    <div className="h-20 pt-6 pb-4 w-full mx-auto flex justify-between items-center">
      <button onClick={handleGotToPage}>
        <LizenzeroLogo width={150} />
      </button>
      <div className="flex items-center gap-3 md:gap-4 lg:gap-6">
        <CountryDropdown localeList={localeList} />
        {children}
        {!session.data && !pathname.includes("login") && (
          <Link href="/auth/login">
            {!isMobile && (
              <Button
                color="yellow"
                variant="filled"
                size="small"
                leadingIcon={<FaUser className="fill-primary size-5" />}
              >
                {t("login")}
              </Button>
            )}
            {isMobile && (
              <Button
                color="yellow"
                variant="filled"
                size="iconXSmall"
                leadingIcon={<FaUser className="fill-primary size-5" />}
              />
            )}
          </Link>
        )}
      </div>
    </div>
  );
}
