"use client";

import { ChangeEvent, useRef, useState } from "react";
import { Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";

import { Popover, PopoverContent, PopoverTrigger, PopoverClose } from "@/components/ui/popover";
import { CountryIcon } from "../../country-icon";
import { enqueueSnackbar } from "notistack";
import { useTranslations } from "next-intl";
import { BaseCountry } from "@/lib/api/country/types";
import { CountryRecommendationModalTrigger } from "@/components/_common/modals/country-recommendation-modal/country-recommendation-modal-trigger";

interface CountryInputProps {
  countries: BaseCountry[];
  onSelectCountry?: (country: BaseCountry) => void;
  recommendationEnabled?: boolean;
  contractedCountryCodes?: string[];
}

export function CountryInput({
  countries,
  onSelectCountry,
  recommendationEnabled = false,
  contractedCountryCodes,
}: CountryInputProps) {
  const [search, setSearch] = useState("");
  const [inputWidth, setInputWidth] = useState<number | undefined>();

  const inputRef = useRef<HTMLInputElement | null>(null);

  const searchCountries =
    search !== ""
      ? countries.filter((c) => c.name.toLocaleLowerCase().includes(search.toLowerCase().trim()))
      : countries;

  const t = useTranslations("shop.common.countryInput");

  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const closePopover = () => closeButtonRef.current?.click();

  return (
    <div className="relative">
      <Popover
        onOpenChange={(open) => {
          if (open && inputRef.current) {
            setInputWidth(inputRef.current.offsetWidth);
          }
        }}
      >
        <PopoverTrigger className="w-full [&>div]:items-start [&>div>div]:w-full [&_input]:w-full" autoFocus={false}>
          <Input
            ref={inputRef}
            leftIcon={<Search width={24} height={24} className="fill-tonal-dark-cream-60" />}
            label={t("label")}
            placeholder={t("placeholder")}
            value={search}
            onChange={(e: ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
          />
        </PopoverTrigger>
        <PopoverContent
          className="shadow-elevation-04-1 pointer-events-auto p-0 py-3 rounded-2xl gap-8 max-h-72 overflow-y-auto border-none"
          style={{ width: inputWidth ? `${inputWidth}px` : "auto" }}
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <PopoverClose asChild>
            <button ref={closeButtonRef} type="button" style={{ visibility: "hidden" }} />
          </PopoverClose>
          {!!searchCountries.length &&
            searchCountries.map((country) => {
              const countryAlreadyPurchased = contractedCountryCodes?.includes(country.code);

              return (
                <button
                  key={country.name}
                  className="py-5 px-4 hover:bg-surface-02 cursor-pointer w-full"
                  onClick={() => {
                    if (countryAlreadyPurchased) return enqueueSnackbar(t("alreadyPurchased"), { variant: "info" });

                    !!onSelectCountry && onSelectCountry(country);

                    setSearch("");

                    inputRef.current?.focus();
                    closePopover();
                  }}
                >
                  <div className="flex items-center gap-4 mb-1">
                    <CountryIcon country={country} className="size-6" />
                    <p className="text-primary">{country.name}</p>
                  </div>
                </button>
              );
            })}
          {!searchCountries.length && (
            <div className="py-5 px-4 gap-4 space-y-2">
              <p className="text-tonal-dark-cream-30 text-ellipsis text-nowrap overflow-hidden">
                {t("noResults", { search })}
              </p>
              {recommendationEnabled && <CountryRecommendationModalTrigger recommendation={search} />}
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
