"use client";

import Script from "next/script";

export function Mouse<PERSON>lowScript() {
  const mouseFlowId = process.env.NEXT_PUBLIC_MOUSE_FLOW_ID;

  if (!mouseFlowId) {
    if (process.env.NODE_ENV !== "production") {
      console.warn("NEXT_PUBLIC_MOUSE_FLOW_ID is not set. MouseFlow script will not be injected.");
    }
    return null;
  }

  return <Script id="mouse-flow" strategy="afterInteractive" src={`//cdn.mouseflow.com/projects/${mouseFlowId}.js`} />;
}
