"use client";

import Script from "next/script";

export function CookieBotScript() {
  const cbid = process.env.NEXT_PUBLIC_COOKIEBOT_ID;

  if (!cbid) {
    if (process.env.NODE_ENV !== "production") {
      console.warn("NEXT_PUBLIC_COOKIEBOT_ID is not set. Cookiebot script will not be injected.");
    }
    return null;
  }

  return (
    <Script
      id="Cookiebot"
      src="https://consent.cookiebot.com/uc.js"
      data-cbid={cbid}
      data-blockingmode="auto"
      type="text/javascript"
      strategy="lazyOnload"
    />
  );
}
