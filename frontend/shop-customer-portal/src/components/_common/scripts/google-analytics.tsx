"use client";

import React from "react";
import Script from "next/script";

export function GoogleAnalyticsScript() {
  const gaId = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;

  if (!gaId) {
    if (process.env.NODE_ENV !== "production") {
      console.warn("NEXT_PUBLIC_GOOGLE_ANALYTICS_ID is not set. Google Analytics script will not be injected.");
    }
    return null;
  }

  return (
    <>
      <Script strategy="lazyOnload" src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`} />
      <Script id="google-analytics" strategy="lazyOnload">
        {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${gaId}');
          `}
      </Script>
    </>
  );
}
