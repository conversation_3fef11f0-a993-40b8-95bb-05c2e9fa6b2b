@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: white;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #ede9e4;
  border-radius: 20px;
}

::-webkit-scrollbar-thumb {
  background: #002652;
  border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.mapboxgl-popup-content {
  padding: 0 !important;
}

/* Hide all mapbox controls except navigation control */
/* .mapboxgl-ctrl-attrib,
.mapboxgl-ctrl-logo {
  display: none !important;
} */

/* Ensure navigation control is visible and properly positioned */
.mapboxgl-ctrl-group {
  display: block !important;
  position: relative !important;
  z-index: 1000 !important;
  box-shadow: none !important;
  gap: 8px;
  display: flex !important;
  flex-direction: column;
  background: none;
  margin-left: 20px !important;
  margin-bottom: 57px !important;
}

.mapboxgl-ctrl-group button {
  display: block !important;
}
button.mapboxgl-ctrl-zoom-in,
button.mapboxgl-ctrl-zoom-out {
  border: none;
  background-color: #707070;
  color: white;
  border-radius: 4px !important;
}
.mapboxgl-ctrl button.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon,
.mapboxgl-ctrl button.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon {
  background-image: none;
}

.mapboxgl-ctrl button.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon::after,
.mapboxgl-ctrl button.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon::after {
  font-size: 1.5rem;
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  margin-top: -0.3rem;
}
.mapboxgl-ctrl button.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon::after {
  content: "+";
}
.mapboxgl-ctrl button.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon::after {
  content: "-";
}

.mapboxgl-map > :last-child {
  height: 0px !important;
}

.select-style {
  border: none;
  border-radius: 4px;
  overflow: hidden;
}

.select-style select {
  border: none;
  box-shadow: none;
  background: transparent;
  background-image: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

input::-ms-reveal,
input::-ms-clear {
  display: none;
}
