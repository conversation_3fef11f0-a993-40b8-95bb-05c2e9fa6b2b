import { CookieBotScript } from "@/components/_common/scripts/cookie-bot";
import { GoogleAnalyticsScript } from "@/components/_common/scripts/google-analytics";
import { MouseFlowScript } from "@/components/_common/scripts/mouse-flow";
import { Providers } from "@/components/providers/providers";
import { cn } from "@/lib/utils";
import { Metadata } from "@stripe/stripe-js";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import localFont from "next/font/local";
import { ReactNode } from "react";

import "@/styles/globals.css";
import "mapbox-gl/dist/mapbox-gl.css";
import { routing } from "@/i18n/routing";
import { notFound } from "next/navigation";

const centraNo2 = localFont({
  src: [
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Thin.woff",
      weight: "100",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Light.woff",
      weight: "300",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Book.woff",
      weight: "400",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Medium.woff",
      weight: "500",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Bold.woff",
      weight: "700",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Extrabold.woff",
      weight: "800",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Black.woff",
      weight: "900",
    },
  ],
  variable: "--font-centra",
});

export const metadata: Metadata = {
  title: "Lizenzero",
  description: "Generated by create next app",
};

interface RootLayoutProps {
  children: ReactNode;
  params: { locale: string };
}

export default async function RootLayout({
  children,
  params: { locale },
}: {
  children: ReactNode;
  params: { locale: string };
}) {
  if (!hasLocale(routing.locales, locale)) return notFound();

  const messages = await getMessages();

  return (
    <html lang={locale} key={locale}>
      <CookieBotScript />
      <GoogleAnalyticsScript />
      <MouseFlowScript />
      <body className={cn("leading-none bg-background", centraNo2.className)}>
        <NextIntlClientProvider messages={messages}>
          <Providers>{children}</Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
