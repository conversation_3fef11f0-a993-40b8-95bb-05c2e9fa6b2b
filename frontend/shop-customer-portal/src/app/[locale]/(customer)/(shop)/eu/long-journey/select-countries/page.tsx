"use client";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { JourneySelectCountries } from "@/components/modules/shop/journeys/components/journey-select-countries";
import { JourneySelectCountriesMap } from "@/components/modules/shop/journeys/components/journey-select-countries/journey-select-countries-map";
import { JourneySelectCountriesProvider } from "@/components/modules/shop/journeys/components/journey-select-countries/journey-select-countries-provider";
import { JourneySelectCountriesSubmit } from "@/components/modules/shop/journeys/components/journey-select-countries/journey-select-countries-submit";
import { Calculator } from "@arthursenno/lizenzero-ui-react/Icon";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

import { useTranslations } from "next-intl";
import { CountryRecommendationModal } from "@/components/_common/modals/country-recommendation-modal";
import { useCallback, useMemo, useState, useEffect } from "react";
import { Country, CountryType } from "@/lib/api/country/types";
import { useCountries } from "@/hooks/use-countries";
import { useSearchParams } from "next/navigation";
import { Icons } from "@/components/ui/icons";
import { ShopLongJourneyStepper } from "@/components/modules/shop/components/shop-stepper";

function byStep<S extends CountryType, T, P>(step: S, home: T, sales: P) {
  return (step === "HOME" ? home : sales) as S extends "HOME" ? T : P;
}

export default function SelectCountries() {
  const t = useTranslations("shop.longJourney.selectCountries");
  const { data: countries } = useCountries();
  const searchParams = useSearchParams();
  const startedOnSales = useMemo(() => searchParams.get("step")?.toLowerCase() === "sales", [searchParams]);
  const [step, setStep] = useState<CountryType>(() => (startedOnSales ? "SALES" : "HOME"));
  const {
    shoppingCart: {
      country: { homeCountry, salesCountries },
    },
    setLocalCart,
  } = useShoppingCart();
  const setHomeCountry = useCallback(
    (country: Country | null) => {
      setLocalCart((data) => {
        return {
          ...data,
          country: {
            ...data.country,
            homeCountry: country,
          },
        };
      });
    },
    [setLocalCart]
  );

  const setSalesCountries = useCallback(
    (countries: Country[] | ((countries: Country[]) => Country[])) => {
      setLocalCart((data) => {
        return {
          ...data,
          country: {
            ...data.country,
            salesCountries: typeof countries === "function" ? countries(data.country.salesCountries) : countries,
          },
        };
      });
    },
    [setLocalCart]
  );

  const onSelectCountry = useCallback(
    (country: Country) => {
      if (step === "HOME") {
        setHomeCountry(country);
      } else {
        setSalesCountries((current) => {
          return [country, ...current];
        });
      }
    },
    [step, setHomeCountry, setSalesCountries]
  );
  const onRemoveCountry = useCallback(
    (country: Country) => {
      if (step === "HOME") {
        setHomeCountry(null);
      } else {
        setSalesCountries((current) => {
          return current.filter((c) => c.code !== country.code);
        });
      }
    },
    [step, setHomeCountry, setSalesCountries]
  );

  const selectedCountries = useMemo(() => {
    if (step === "HOME") {
      return homeCountry ? [homeCountry] : [];
    }
    return salesCountries;
  }, [step, homeCountry, salesCountries]);

  const countryOptions = useMemo(() => {
    return byStep(
      step,
      countries,
      countries.filter((c) => c.license_required)
    );
  }, [step, countries]);

  const onNext = useCallback(() => {
    if (step === "HOME") {
      setStep("SALES");
    } else {
      location.href = "./obligations";
    }
  }, [step]);
  const onBack = useCallback(() => {
    if (step === "SALES") {
      setStep("HOME");
    }
  }, [step]);

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Calculator} />
      <ShopLongJourneyStepper stepById={"select-countries"} />
      <ShopContent containerClassName="overflow-hidden mb-20">
        <div className="grid grid-cols-1 md:grid-cols-2 pb-4 md:pb-16 gap-4">
          <div>
            <div className="flex items-center gap-4">
              <div className="flex flex-row items-center gap-2">
                <p className={`text-primary md:text-5xl text-3xl font-semibold md:leading-h1`}>
                  {t(byStep(step, "titleHome", "titleEu"))}
                </p>
              </div>
            </div>
            <p className="text-primary text-base leading-5">
              {t.rich(byStep(step, "selectInMapHome", "selectInMapEu"), {
                bold: (chunks) => <strong>{chunks}</strong>,
              })}
            </p>
          </div>

          <div className="hidden md:flex items-center">
            <div className="bg-tonal-dark-blue-90 rounded-[52px] flex items-center p-8 gap-6 w-full">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center z-10 flex-none">
                <Icons.euStars className="fill-tonal-dark-blue-90 w-12 h-12" />
              </div>

              <div className="space-y-2 z-10">
                <p className="text-primary font-bold text-xl">{t("benefit.title")}</p>
                <p className="text-primary">{t("benefit.description")}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <JourneySelectCountriesProvider>
            <div>
              <JourneySelectCountries
                title={t(byStep(step, "countrySelectionTitleHome", "countrySelectionTitleEu"))}
                countries={countryOptions}
                onSelect={onSelectCountry}
                onRemove={onRemoveCountry}
                selected={selectedCountries}
                enableInput={byStep(step, !homeCountry, true)}
              />
            </div>
            <div className="space-y-8 md:space-y-10">
              <JourneySelectCountriesMap
                countries={countryOptions}
                selected={selectedCountries}
                onSelect={onSelectCountry}
              />
              <JourneySelectCountriesSubmit
                disabled={byStep(step, !homeCountry, salesCountries.length === 0)}
                onNext={onNext}
                onBack={byStep(step, null, onBack)}
              />
            </div>
          </JourneySelectCountriesProvider>
        </div>
      </ShopContent>
      <CountryRecommendationModal />
    </>
  );
}
