"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import {
  Calculator,
  ControllerPainel,
  Lightbulb,
  Padlock,
  WorkspacePremium,
} from "@arthursenno/lizenzero-ui-react/Icon";

import { SelectCountriesMapModal } from "@/components/_common/modals/select-countries-map-modal";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";

import { ShopTrustpilot } from "@/components/modules/shop/components/shop-trustpilot";
import { JourneyCalculatorProvider } from "@/components/modules/shop/journeys/components/journey-calculator/journey-calculator-provider";
import { JourneyCalculatorSubmit } from "@/components/modules/shop/journeys/components/journey-calculator/journey-calculator-submit";
import { JourneyResume } from "@/components/modules/shop/journeys/components/journey-resume";

import { JourneyCalculatorItem } from "@/components/modules/shop/journeys/components/journey-calculator/journey-calculator-item";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";
import { useShoppingCart } from "@/hooks/use-shopping-cart";

import { useTranslations } from "next-intl";
import { JourneyShoppingCartCard } from "@/components/modules/shop/journeys/components/journey-shopping-cart-card";

export default function Calculate() {
  const { shoppingCart } = useShoppingCart();
  const t = useTranslations("shop.longJourney.calculator");

  const licenseItems = shoppingCart.items.filter((i) => i.service_type === "EU_LICENSE");

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Calculator} />
      <ShopLicenseStepper step={1} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <SelectCountriesMapModal />
        <div className="pb-10 md:pb-32">
          <div className="flex flex-col md:flex-row gap-6 w-full justify-between">
            <JourneyCalculatorProvider>
              <div className="flex flex-col gap-6 flex-1">
                {licenseItems.map((item) => (
                  <JourneyCalculatorItem key={`${item.country_code}-calculator-item`} cartItem={item} />
                ))}
              </div>

              <div className="flex flex-col gap-10 md:gap-16 w-full md:max-w-[40%] flex-1">
                <ShopBanner title="" style={{ width: "100%" }}>
                  <div className="flex flex-col gap-10">
                    <div className="flex items-start">
                      <IconBanner
                        className="text-white "
                        icon={() => <Lightbulb width={24} height={24} className="fill-tonal-dark-blue-80" />}
                      />

                      <div>
                        <p className="font-bold text-base">{t("banners.first.title")}</p>
                        <span className="w-full text-sm ">{t("banners.first.description")}</span>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <IconBanner
                        className="text-white "
                        icon={() => <ControllerPainel width={24} height={24} className="fill-tonal-dark-blue-80" />}
                      />

                      <div>
                        <p className="font-bold text-base">{t("banners.second.title")}</p>
                        <span className="w-full text-sm ">{t("banners.second.description")}</span>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <IconBanner
                        className="text-white"
                        icon={() => <WorkspacePremium width={24} height={24} className="fill-tonal-dark-blue-80" />}
                      />

                      <div>
                        <p className="font-bold text-base">{t("banners.third.title")}</p>
                        <span className="w-full text-sm ">{t("banners.third.description")}</span>
                      </div>
                    </div>
                  </div>
                </ShopBanner>

                <JourneyShoppingCartCard defaultLayout="mini" />

                <div className="space-y-6">
                  <JourneyCalculatorSubmit />
                  <ShopTrustpilot />
                </div>

                <ShopBanner title="" style={{ width: "100%" }}>
                  <IconBanner
                    className="text-white "
                    icon={() => <Padlock width={24} height={24} className="fill-tonal-dark-blue-80" />}
                  />

                  <div>
                    <p className="font-bold text-base">{t("banners.fourth.title")}</p>
                    <span className="w-full text-sm ">{t("banners.fourth.description")}</span>
                  </div>
                </ShopBanner>
              </div>
            </JourneyCalculatorProvider>
          </div>
        </div>
      </ShopContent>
    </>
  );
}
