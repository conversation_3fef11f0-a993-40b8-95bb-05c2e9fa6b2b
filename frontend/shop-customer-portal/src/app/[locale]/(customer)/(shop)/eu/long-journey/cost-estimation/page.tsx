"use client";

import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Calculator, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { JourneyCalculatorProvider } from "@/components/modules/shop/journeys/components/journey-calculator/journey-calculator-provider";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";
import { useTranslations } from "next-intl";
import { JourneyShoppingCartCard } from "@/components/modules/shop/journeys/components/journey-shopping-cart-card";
import { cn } from "@/lib/utils";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Icons } from "@/components/ui/icons";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";

export default function CostEstimation() {
  const t = useTranslations("shop.longJourney.costEstimation");
  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Calculator} />
      <ShopContent>
        <div className={cn("pb-8 md:pb-16 max-w-screen-md")}>
          <div className="flex items-center gap-4">
            <div className="flex flex-row items-center gap-2">
              <p className={`text-primary md:text-5xl text-3xl font-semibold md:leading-[62.4px]`}>{t("title")}</p>
              <p className={`text-primary md:text-3xl text-xl md:leading-[62.4px]`}>{t("optional")}</p>
            </div>
          </div>
          <p className="text-primary pr-32">{t("main.description")}</p>
        </div>
        <div className="pb-10 md:pb-32">
          <div className="flex flex-col md:flex-row gap-6 w-full justify-between">
            <JourneyCalculatorProvider>
              {/* Left */}
              <div className="flex flex-col gap-6 flex-1">
                {/* Note */}
                <div>
                  <div className="p-4 bg-surface-01 rounded-[20px] flex flex-row items-center gap-3">
                    <Error className="fill-primary size-6" />
                    <p className="text-primary text-base leading-5 flex-1">{t("main.note")}</p>
                  </div>
                </div>
                {/* TODO: implement calculator */}
                <div className="p-10 bg-tonal-dark-cream-96 rounded-[40px]">
                  <p className="text-primary text-2xl font-bold">{t("main.estimation.title")}</p>
                  <p className="text-tonal-dark-cream-30 text-base mt-5">{t("main.estimation.description")}</p>
                  <Button
                    className="w-full mt-8"
                    trailingIcon={<East />}
                    variant="outlined"
                    color="dark-blue"
                    size="small"
                    // TODO: add actual logic
                    onClick={() => {}}
                  >
                    {t("main.estimation.btnLabel")}
                  </Button>
                </div>
              </div>
              {/* Right */}
              <div className="flex flex-col gap-10 w-full md:max-w-[40%] flex-1">
                <ShopBanner className="bg-tonal-dark-blue-96 !w-full py-12 px-8" title="">
                  <div className="flex flex-col gap-10">
                    <div className="flex items-start">
                      <IconBanner
                        className="text-white "
                        icon={() => <Icons.lock className="fill-tonal-dark-blue-80" />}
                      />

                      <div>
                        <p className="font-bold text-base">{t("banners.first.title")}</p>
                        <span className="w-full text-sm ">{t("banners.first.description")}</span>
                      </div>
                    </div>
                  </div>
                </ShopBanner>
                <JourneyShoppingCartCard defaultLayout="mini" />
                <div className="space-y-6">
                  <Button
                    className="w-full"
                    trailingIcon={<East />}
                    color={"yellow"}
                    variant="filled"
                    size="medium"
                    // TODO: add actual logic
                    onClick={() => {}}
                    type="button"
                  >
                    {t("main.submitLabel")}
                  </Button>
                </div>
              </div>
            </JourneyCalculatorProvider>
          </div>
        </div>
      </ShopContent>
    </>
  );
}
