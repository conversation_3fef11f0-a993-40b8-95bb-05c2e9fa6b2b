import { API } from "./api";
import { StrapiActionGuide } from "./types";

const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL;

if (!strapiUrl) {
  if (process.env.NODE_ENV !== "production") {
    console.warn("⚠️  WARNING: NEXT_PUBLIC_STRAPI_URL is not set. Strapi integration will not work.");
    console.warn("   This environment variable is required for CMS content management.");
  }
  throw new Error("Missing env variable: NEXT_PUBLIC_STRAPI_URL");
}

type StrapiFindAllResponse<T = unknown> = {
  data: T;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
};

type StrapiFindByIdResponse<T = unknown> = {
  data: T;
  meta: unknown;
};

class Strapi {
  private api: API;

  constructor(strapiUrl: string) {
    this.api = new API(`${strapiUrl}/api`);
  }

  async getActionGuideByCountryCode(countryCode: string): Promise<StrapiActionGuide | null> {
    try {
      const response = await this.api.get<StrapiFindAllResponse<StrapiActionGuide[]>>(
        `/action-guides?filters[countryCode][$eq]=${countryCode}&populate[modules][populate]=*&populate[generalInformation][populate]=*&populate=*`
      );
      return response.data[0];
    } catch {
      return null;
    }
  }
}

export const strapi = new Strapi(strapiUrl);
