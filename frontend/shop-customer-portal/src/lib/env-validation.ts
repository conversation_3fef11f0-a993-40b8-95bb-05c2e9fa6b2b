/**
 * Environment Variables Validation
 * This module validates all environment variables and logs warnings for missing ones
 */

interface EnvVariable {
  name: string;
  value: string | undefined;
  required: boolean;
  description: string;
}

class EnvironmentValidator {
  private variables: EnvVariable[] = [];
  private hasErrors = false;
  private hasWarnings = false;

  constructor() {
    this.initializeVariables();
  }

  private initializeVariables() {
    // Core required variables
    this.variables.push({
      name: "NEXTAUTH_SECRET",
      value: process.env.NEXTAUTH_SECRET,
      required: true,
      description: "Secret key for NextAuth.js authentication",
    });

    this.variables.push({
      name: "API",
      value: process.env.API,
      required: true,
      description: "Backend API URL for server-side requests",
    });

    // Public required variables
    this.variables.push({
      name: "NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN",
      value: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN,
      required: true,
      description: "Mapbox access token for map functionality",
    });

    // Optional but recommended variables
    this.variables.push({
      name: "NEXT_PUBLIC_DOMAIN",
      value: process.env.NEXT_PUBLIC_DOMAIN,
      required: false,
      description: "Application domain URL for callbacks and redirects",
    });

    this.variables.push({
      name: "NEXT_PUBLIC_STRAPI_URL",
      value: process.env.NEXT_PUBLIC_STRAPI_URL,
      required: false,
      description: "Strapi CMS URL for content management",
    });

    this.variables.push({
      name: "NEXT_PUBLIC_LANDING_PAGE_URL",
      value: process.env.NEXT_PUBLIC_LANDING_PAGE_URL,
      required: false,
      description: "Landing page URL for navigation",
    });

    // Payment related
    this.variables.push({
      name: "NEXT_PUBLIC_STRIPE_PUBLIC_KEY",
      value: process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY,
      required: false,
      description: "Stripe public key for payment processing",
    });

    this.variables.push({
      name: "SECRET_STRIPE_KEY",
      value: process.env.SECRET_STRIPE_KEY,
      required: false,
      description: "Stripe secret key for server-side payment processing",
    });

    // Analytics and tracking
    this.variables.push({
      name: "NEXT_PUBLIC_MOUSE_FLOW_ID",
      value: process.env.NEXT_PUBLIC_MOUSE_FLOW_ID,
      required: false,
      description: "MouseFlow tracking ID for user behavior analytics",
    });

    this.variables.push({
      name: "NEXT_PUBLIC_GOOGLE_ANALYTICS_ID",
      value: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
      required: false,
      description: "Google Analytics ID for website analytics",
    });

    this.variables.push({
      name: "NEXT_PUBLIC_COOKIEBOT_ID",
      value: process.env.NEXT_PUBLIC_COOKIEBOT_ID,
      required: false,
      description: "Cookiebot ID for cookie consent management",
    });

    // System API
    this.variables.push({
      name: "SYSTEM_API_KEY",
      value: process.env.SYSTEM_API_KEY,
      required: false,
      description: "System API key for server-side authentication",
    });

    // Gateway URLs
    this.variables.push({
      name: "NEXT_PUBLIC_CUSTOMER_GATEWAY_URL",
      value: process.env.NEXT_PUBLIC_CUSTOMER_GATEWAY_URL,
      required: false,
      description: "Customer gateway URL for API requests",
    });

    this.variables.push({
      name: "NEXT_PUBLIC_ADMIN_GATEWAY_URL",
      value: process.env.NEXT_PUBLIC_ADMIN_GATEWAY_URL,
      required: false,
      description: "Admin gateway URL for administrative API requests",
    });

    this.variables.push({
      name: "NEXT_PUBLIC_API_URL",
      value: process.env.NEXT_PUBLIC_API_URL,
      required: false,
      description: "Public API URL for client-side requests",
    });

    // NextAuth URL (optional but recommended in production)
    this.variables.push({
      name: "NEXTAUTH_URL",
      value: process.env.NEXTAUTH_URL,
      required: false,
      description: "NextAuth callback URL (auto-detected in most cases)",
    });
  }

  private formatMessage(variable: EnvVariable, type: "error" | "warning") {
    const prefix = type === "error" ? "❌ ERROR" : "⚠️  WARNING";
    const color = type === "error" ? "\x1b[31m" : "\x1b[33m"; // Red for error, Yellow for warning
    const reset = "\x1b[0m";

    return `${color}${prefix}: Environment variable '${variable.name}' is not set.${reset}\n   ${variable.description}`;
  }

  public validate() {
    console.log("\n🔍 Validating environment variables...\n");

    const missingRequired: EnvVariable[] = [];
    const missingOptional: EnvVariable[] = [];

    for (const variable of this.variables) {
      if (!variable.value || variable.value.trim() === "") {
        if (variable.required) {
          missingRequired.push(variable);
          this.hasErrors = true;
        } else {
          missingOptional.push(variable);
          this.hasWarnings = true;
        }
      }
    }

    // Log required variables errors
    if (missingRequired.length > 0) {
      console.error("═══════════════════════════════════════════════════════════");
      console.error("CRITICAL: Missing required environment variables");
      console.error("═══════════════════════════════════════════════════════════");
      missingRequired.forEach((variable) => {
        console.error(this.formatMessage(variable, "error"));
      });
      console.error("═══════════════════════════════════════════════════════════\n");
    }

    // Log optional variables warnings
    if (missingOptional.length > 0) {
      console.warn("───────────────────────────────────────────────────────────");
      console.warn("Optional environment variables not configured");
      console.warn("───────────────────────────────────────────────────────────");
      missingOptional.forEach((variable) => {
        console.warn(this.formatMessage(variable, "warning"));
      });
      console.warn("───────────────────────────────────────────────────────────\n");
    }

    // Summary
    if (!this.hasErrors && !this.hasWarnings) {
      console.log("✅ All environment variables are properly configured!\n");
    } else {
      const summary = [];
      if (this.hasErrors) {
        summary.push(`${missingRequired.length} required variable(s) missing`);
      }
      if (this.hasWarnings) {
        summary.push(`${missingOptional.length} optional variable(s) not set`);
      }
      console.log(`📊 Summary: ${summary.join(", ")}\n`);
    }

    return {
      isValid: !this.hasErrors,
      hasWarnings: this.hasWarnings,
      missingRequired,
      missingOptional,
    };
  }

  public getVariable(name: string): string | undefined {
    const variable = this.variables.find((v) => v.name === name);
    return variable?.value;
  }

  public isProduction(): boolean {
    return process.env.NODE_ENV === "production";
  }

  public isDevelopment(): boolean {
    return process.env.NODE_ENV === "development";
  }
}

// Create singleton instance
const envValidator = new EnvironmentValidator();

// Export validation function
export function validateEnvironment() {
  return envValidator.validate();
}

// Export helper to get validated environment variable
export function getEnvVariable(name: string): string | undefined {
  const value = envValidator.getVariable(name);

  if (!value && process.env.NODE_ENV !== "production") {
    console.warn(`⚠️  Environment variable '${name}' is not set`);
  }

  return value;
}

// Export environment checker utilities
export const isProduction = () => envValidator.isProduction();
export const isDevelopment = () => envValidator.isDevelopment();

// Auto-validate on module load (only in development)
if (typeof window === "undefined" && process.env.NODE_ENV !== "production") {
  validateEnvironment();
}
