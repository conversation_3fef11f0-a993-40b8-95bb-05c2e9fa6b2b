export interface BaseCountry {
  id: number;
  code: string;
  name: string;
  flag_url: string;
}

export interface Country extends BaseCountry {
  id: number;
  name: string;
  code: string;
  authorize_representative_obligated: boolean;
  other_costs_obligated: boolean;
  is_published: boolean;
  license_required: boolean;
  created_at: string;
  updated_at: string;
}

export type CountryType = "HOME" | "SALES";
