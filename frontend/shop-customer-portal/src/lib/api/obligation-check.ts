"use client";

import { api } from "@/lib/api";

export interface Country {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  authorize_representative_obligated: boolean;
  code: string;
  flag_url: string;
  other_costs_obligated: boolean;
  is_published: boolean;
  criterias: ObligationCriteria[];
  license_required: boolean;
  has_authorize_representative_criteria: boolean;
  has_other_cost_criteria: boolean;
  has_representative_tier_criteria: boolean;
}

export interface ObligationCheckSection {
  id: number;
  title: string;
  display_order: number;
  created_at: string;
  updated_at: string;
  country_id: number;
}

export interface ObligationCriteria {
  id: number;
  created_at: string;
  deleted_at: string | null;
  help_text?: string;
  input_type: "YES_NO";
  mode: "COMMITMENT";
  title: string;
  type: "PACKAGING_SERVICE";
  updated_at: string;
  calculator_type: string | null;
  country_id: number;
  packaging_service_id: number;
  required_information_id: number | null;
  obligation_check_section_id: number;
  options: ObligationOption[];
}

export interface ObligationOption {
  id: number;
  value: "OBLIGED" | "NOT_OBLIGED" | "CONDITIONAL";
  option_value: "YES" | "NO";
  option_to_value: string | null;
  packaging_services: Array<{ id: number; name: string }>;
  conditional_criteria_id: number | null;
}

export async function getCountryByCode(countryCode: string): Promise<Country> {
  try {
    const response = await api.get(`/admin/countries/code/${countryCode}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch country ${countryCode}:`, error);
    throw error;
  }
}

export async function getObligationCheckSectionsByCountryCode(countryCode: string): Promise<ObligationCheckSection[]> {
  try {
    const response = await api.get(`/admin/obligation-check-sections/country/${countryCode}`);
    return response.data || [];
  } catch (error) {
    console.error(`Failed to fetch obligation check sections for country ${countryCode}:`, error);
    return [];
  }
}

export async function getCriteriasBySectionId(sectionId: number): Promise<ObligationCriteria[]> {
  try {
    const response = await api.get(`/admin/criterias/section/${sectionId}`);
    return response.data || [];
  } catch (error) {
    console.error(`Failed to fetch criteria for section ${sectionId}:`, error);
    return [];
  }
}

export interface ObligationCheckAnswer {
  criteriaId: number;
  answer: "YES" | "NO";
}

export interface ObligationCheckSubmission {
  countryCode: string;
  sectionId: number;
  answers: ObligationCheckAnswer[];
}

export interface ObligationCheckSubmissionResponse {
  success: boolean;
  message?: string;
  obligation_status?: "OBLIGED" | "NOT_OBLIGED" | "PARTIAL";
}

export async function submitObligationCheckAnswers(
  submission: ObligationCheckSubmission
): Promise<ObligationCheckSubmissionResponse> {
  // TODO: Implement when submission endpoint is ready
  console.log("📤 Would submit to API:", submission);

  // Mock response for now
  const mockResponse: ObligationCheckSubmissionResponse = {
    success: true,
    message: `Section ${submission.sectionId} submitted successfully`,
    obligation_status: "OBLIGED",
  };

  return mockResponse;

  // TODO: Uncomment this when backend is ready
  // try {
  //   const response = await api.post(`/api/obligation-check/submit`, submission);
  //   return response.data;
  // } catch (error) {
  //   console.error("Failed to submit obligation check answers:", error);
  //   throw error;
  // }
}
