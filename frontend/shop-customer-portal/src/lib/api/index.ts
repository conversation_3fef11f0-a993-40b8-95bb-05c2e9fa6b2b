import axiosInstance from "axios";
import { signOut } from "next-auth/react";
import { tokenManager } from "../next-auth/local-token-manager";

const api = axiosInstance.create({
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
  baseURL: "/api-oneepr",
});

api.interceptors.request.use(async (config) => {
  if (typeof window === "undefined") {
    if (process.env.SYSTEM_API_KEY) {
      config.headers["X-System-Api-Key"] = process.env.SYSTEM_API_KEY;
    } else if (process.env.NODE_ENV !== "production") {
      console.warn("⚠️  WARNING: SYSTEM_API_KEY is not set. Server-side API authentication may fail.");
    }

    return config;
  }

  const accessToken = tokenManager.getAccessToken();

  if (!accessToken) return config;

  config.headers.Authorization = `Bearer ${accessToken}`;

  return config;
});

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const errorUrl = error?.config?.url;

    // TODO: Temporarily disabled automatic redirect to login on 401 errors
    // This needs to be revisited during the planned refactoring as the current
    // authentication flow requires a different approach
    // if (error.response?.status === 401 && !errorUrl.includes("/change-")) {
    //   await signOut({ callbackUrl: "/auth/login" });
    // }

    return Promise.reject(error);
  }
);

export { api };
