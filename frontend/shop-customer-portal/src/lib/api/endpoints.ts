export const ApiEndpoints = {
  commission: {
    get: `/customer/commission`,
    patchUse: (userId: number) => `/customer/commission/use/${userId}`,
    postCreateCoupon: (userId: number) => `/customer/commission/generate_coupon/${userId}`,
  },
  user: {
    create: "/auth/user",
    createPassword: `/auth/user/account/create-password`,
    findByEmail: (email: string) => `/auth/user/request/email/${email}`,
    getStatus: (email: string) => `/auth/user/status/${email}`,
    postVerify: `/auth/user/account/verify`,
    postVerificationCode: `/auth/user/account/verify/sendToken`,
    postVerificationMagicToken: `/auth/user/account/verify/magic-link`,
    postConfirmVerificationCode: `/auth/user/account/verify/confirmToken`,
    postResendToken: `/auth/user/account/verify/resend-token`,
    postLogin: `/account/login`,
    postRecoverPassword: `/auth/user/reset/password`,
    postForgotPassword: `/auth/user/request/password`,
    patchPassword: (id: string | number) => `/auth/user/change-password/${id}`,
    patchEmail: (id: string | number) => `/auth/user/change-email/${id}`,
    patchVerifyEmail: (id: string | number) => `/auth/user/change-email/verify/${id}`,
    putPersonalData: `/account`,
  },
  customer: {
    create: "/customer/customers",
    getCustomer: (customerId: number) => `/customer/customers/${customerId}`,
    getCustomerByUserId: (userId: number) => `/customer/customers/user/${userId}`,
    putCustomerByUserId: (userId: number | string) => `/customer/customers/${userId}`,
    tutorials: "/customer/customers/tutorials",
  },
  company: {
    findById: (companyId: number) => `/customer/company/${companyId}`,
    findByParnterId: (partnerId: number) => `/customer/company/partner/${partnerId}`,
    create: "/customer/company",
    update: (companyId: number) => `/customer/company/${companyId}`,
    lucidId: "/customer/company/lucidId",
    validateVatId: "/customer/company/validate-vat",
  },
  paymentCustomer: {
    create: "/payment/payment-customer",
    getMany: "/payment/payment-customer",
    getOne: (id: string) => `/payment/payment-customer/${id}`,
    update: (id: string) => `/payment/payment-customer/${id}`,
    delete: (id: string) => `/payment/payment-customer/${id}`,
    getOneByCustomerId: (customerId: number) => `/payment/payment-customer/customer/${customerId}`,
    setDefaultPaymentMethod: (customerId: number) =>
      `/payment/payment-customer/customer/${customerId}/default-payment-method`,
  },
  certificate: {
    getAll: "/customer/certificates",
  },
  thirdPartyInvoice: {
    getAll: "/customer/third-party-invoices",
    findById: (thirdPartyInvoiceId: number) => `/customer/third-party-invoices/${thirdPartyInvoiceId}`,
  },
  partner: {
    postCreate: `/customer/partner`,
    getPartnerByUserId: (userId: string | number) => `/customer/partner/user/${userId}`,
    patchPartner: (partnerId: number | string) => `/customer/partner/${partnerId}`,
    signContractPartner: (partnerId: number | string) => `/customer/partner/sign-contract/${partnerId}`,
    patchPartnerByUserId: (userId: number | string) => `/customer/partner/${userId}`,
    checkCredentials: (userId: number | string) => `/auth/user/${userId}`,
  },
  volume: {
    getVolume: (dealId: number | string) => `/customer/volumes/by-deal/${dealId}`,
    postVolume: `/customer/volumes`,
    patchVolume: (dealId: number | string) => `/customer/volumes/by-deal/${dealId}`,
  },
  country: {
    getAllCountries: `/countries`,
    getAdminCountries: `/admin/countries`,
    getSearchCountry: (countryName: string) => `/Country?Search=${countryName}`,
    countries: "/countries",
    getAllCountryDocuments: `/contract_documents`,
    recommend: `/customer/RecommendCountry`,
  },
  coupon: {
    getByCode: (code: string) => `/customer/coupon/code/${code}`,
  },
  license: {
    getAll: `/customer/licenses`,
    getOne: (licenseId: number) => `/customer/licenses/${licenseId}`,
  },
  calculator: {
    postAddPackagingVolume: `/License/Calculator/PackagingVolume`,
  },
  dashboard: {
    getSingleDashboard: (userId: number) => `/Dashboard/${userId}`,
  },
  order: {
    getAll: "/payment/payment-order",
    getOne: (orderId: string) => `/payment/payment-order/${orderId}`,
  },
  invoice: {
    create: "/payment/invoice",
    get: `/payment/invoice`,
    getOne: (invoiceId: string) => `/payment/invoice/${invoiceId}`,
  },
  paymentInformations: {
    getByUserId: (userId: string) => `/payment/payment-informations/${userId}`,
    getByPaymentCustomerId: (paymentCustomerId: string) => `/payment/payment-informations/${paymentCustomerId}`,
    patch: (customerId: number) => `/payment/payment-informations/${customerId}`,
  },
  invite: {
    getUserInvites: (userId: number) => `/customer/invite/${userId}`,
    postInviteCode: (userId: number) => `/customer/invite/code/${userId}`,
  },
  shoppingCart: {
    post: "/customer/shopping-cart",
    create: "/customer/shopping-cart",
    update: (shoppingId: string) => `/customer/shopping-cart/${shoppingId}`,
    getById: (shoppingId: string) => `/customer/shopping-cart/${shoppingId}`,
    getByEmail: (email: string) => `/customer/shopping-cart/email/${email}`,
    getPurchasedByEmail: (email: string) => `/customer/shopping-cart/purchased/${email}`,
    items: {
      create: (shoppingId: string) => `/customer/shopping-cart/${shoppingId}/items`,
      update: (shoppingId: string, itemId: number) => `/customer/shopping-cart/${shoppingId}/items/${itemId}`,
      delete: (shoppingId: string, itemId: number) => `/customer/shopping-cart/${shoppingId}/items/${itemId}`,
    },
  },
  addressSuggestions: {
    getSuggestions: "/api/address/suggestions",
    getDetails: "/api/address/suggestions/details",
  },
  consent: {
    getOne: (id: number) => `/customer/consent/${id}`,
    getAll: "/customer/consent",
    create: "/customer/consent",
    update: (id: number) => `/customer/consent/${id}`,
    getByType: (type: string) => `/customer/consent/type/${type}`,
    delete: (id: number) => `/customer/consent/${id}`,
  },
  customerConsent: {
    create: "/customer/customer-consent",
    createMany: "/customer/customer-consent/create-many",
    getOne: (id: number) => `/customer/customer-consent/${id}`,
    getAll: "/customer/customer-consent",
    getByCustomerId: (customerId: number) => `/customer/customer-consent/customer/${customerId}`,
    update: (id: number) => `/customer/customer-consent/${id}`,
    updateMany: "/customer/customer-consent/update-many",
    delete: (id: number) => `/customer/customer-consent/${id}`,
  },
  paymentMethod: {
    create: "/payment/payment-method",
    getMany: "/payment/payment-method",
    getActiveByShopCustomerId: (id: number) => `/payment/payment-method/customer/${id}`,
    getOne: (paymentMethodId: string) => `/payment/payment-method/${paymentMethodId}`,
    update: (paymentMethodId: string) => `/payment/payment-method/${paymentMethodId}`,
    delete: (paymentMethodId: string) => `/payment/payment-method/${paymentMethodId}`,
  },
  payment: {
    create: "/payment/payment",
    generateInvoice: "/payment/payment/generate-invoice",
    payInvoice: (invoiceId: number) => `/payment/payment/invoice/${invoiceId}`,
    createWithOrder: "/payment/payment/with-order",
    getMany: "/payment/payment",
    getOne: (paymentId: string) => `/payment/payment/${paymentId}`,
    update: (paymentId: string) => `/payment/payment/${paymentId}`,
    delete: (paymentId: string) => `/payment/payment/${paymentId}`,
    pain: "payment/commerzbank/submit-pain001",
    payOrder: (orderId: number) => `/payment/purchase/${orderId}`,
    createCheckoutSession: "/payment/purchase/checkout",
    getCheckoutSession: (sessionId: string) => `/payment/purchase/checkout/${sessionId}`,
  },
  purchase: {
    create: "/customer/purchases",
  },
  stripe: {
    createCustomer: "/payment/stripe/customer",
    createPaymentIntent: "/payment/stripe/payment-intent",
  },
  uploadFiles: {
    getAll: `/upload-files`,
    getFileToDownload: `/upload-files/files`,
    downloadAdminFile: (fileId: string) => `/admin/upload-files/${fileId}`,
    downloadCustomerFile: (fileId: string) => `/customer/files/${fileId}`,
    downloadCustomerFileByRelation: "/customer/files/relation",
    post: `/customer/files`,
    delete: (fileId: string) => `/customer/files/${fileId}`,
  },
  contracts: {
    findAll: "/customer/contracts",
    findById: (id: number) => `/customer/contracts/${id}`,
  },
  requiredInformations: {
    getAll: `/customer/required-informations`,
    update: (requiredInformationId: number) => `/customer/required-informations/${requiredInformationId}`,
    updateAnswer: (requiredInformationId: number) => `/customer/required-informations/${requiredInformationId}`,
  },
  packagingServices: {
    getAll: "/customer/packaging-services",
    findById: (packagingServiceId: number) => `/customer/packaging-services/${packagingServiceId}`,
  },
  volumeReport: {
    create: "/customer/license-volume-report",
    update: (volumeReportId: number) => `/customer/license-volume-report/${volumeReportId}`,
  },
  volumeReportItem: {
    create: "/customer/license-volume-report-item/bulk",
    getByVolumeReportId: (volumeReportId: number) =>
      `/customer/license-volume-report-item?license_volume_report_id=${volumeReportId}`,
  },
  serviceNextSteps: {
    getAll: "/customer/service-next-steps",
    update: (serviceNextStepId: number) => `/customer/service-next-steps/${serviceNextStepId}`,
  },
  purchaseServices: {
    purchase: "/customer/purchases",
  },
  thirdPartyInvoices: {
    findAll: `/customer/third-party-invoices`,
  },
  termination: {
    create: "/customer/terminations",
    revoke: (terminationId: number) => `/customer/terminations/${terminationId}/revoke`,
  },
  marketingMaterials: {
    get: "/customer/marketing-materials",
  },
  reasons: {
    getAll: "/customer/reasons",
  },
};
