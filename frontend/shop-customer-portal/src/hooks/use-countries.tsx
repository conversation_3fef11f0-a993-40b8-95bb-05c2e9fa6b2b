import { getAdminCountries } from "@/lib/api/country";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useCountries() {
  const t = useTranslations("shop.common.liberatedCountries");
  const {
    data,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["admin-countries"],
    queryFn: getCountries,
  });

  async function getCountries() {
    try {
      const [liberatedResponse, nonLiberatedResponse] = await Promise.all([
        getAdminCountries(true),
        getAdminCountries(false),
      ]);

      if (!liberatedResponse || !nonLiberatedResponse) {
        throw new Error(t("error"));
      }

      if (liberatedResponse.status >= 400 || nonLiberatedResponse.status >= 400) {
        throw new Error(t("error"));
      }

      const liberatedCountries = liberatedResponse.data ?? [];
      const nonLiberatedCountries = nonLiberatedResponse.data ?? [];

      const allCountries = [...liberatedCountries, ...nonLiberatedCountries];

      const seenIds = new Set();
      const uniqueCountries = allCountries.filter((country) => {
        if (country?.id && !seenIds.has(country.id)) {
          seenIds.add(country.id);
          return true;
        }
        return false;
      });

      return uniqueCountries.sort((a, b) => a.name.localeCompare(b.name, undefined, { sensitivity: "base" }));
    } catch (error) {
      throw error instanceof Error ? error : new Error(t("error"));
    }
  }

  return {
    data: data || [],
    loading,
    error: error?.message || t("error"),
  };
}
