"use client";

import { useCallback, useMemo } from "react";
import { useShoppingCart } from "./use-shopping-cart";

interface ObligationCompletionState {
  [countryCode: string]: boolean;
}

export function useObligationCompletion() {
  const { obligationStorage, updateCountryObligation, shoppingCart } = useShoppingCart();

  // Generate completion status from local storage - reactive to changes
  const completionStatus = useMemo((): ObligationCompletionState => {
    const status: ObligationCompletionState = {};

    // Only check completion for sales countries, not random storage entries
    const salesCountries = shoppingCart.country.salesCountries || [];

    salesCountries.forEach((country) => {
      const countryCode = country.code;
      status[countryCode] = obligationStorage[countryCode]?.completed || false;
    });

    // console.log("📊 Completion status updated:", status);
    // console.log(
    //   "🌍 Sales countries:",
    //   salesCountries.map((c) => c.code)
    // );
    // console.log("💾 Storage keys:", Object.keys(obligationStorage));
    return status;
  }, [obligationStorage, shoppingCart.country.salesCountries]);

  const getCompletionStatus = useCallback(() => {
    return completionStatus;
  }, [completionStatus]);

  const markCountryComplete = useCallback(
    (countryCode: string) => {
      updateCountryObligation(countryCode, {
        completed: true,
      });
    },
    [updateCountryObligation]
  );

  const markCountryIncomplete = useCallback(
    (countryCode: string) => {
      updateCountryObligation(countryCode, {
        completed: false,
      });
    },
    [updateCountryObligation]
  );

  const isCountryComplete = useCallback(
    (countryCode: string) => {
      return obligationStorage[countryCode]?.completed || false;
    },
    [obligationStorage]
  );

  return {
    completionStatus,
    markCountryComplete,
    markCountryIncomplete,
    isCountryComplete,
    getCompletionStatus,
  };
}
