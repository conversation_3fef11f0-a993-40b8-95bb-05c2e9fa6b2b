import createNextIntlPlugin from "next-intl/plugin";

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    remotePatterns: [
      { hostname: "upload.wikimedia.org" },
      { hostname: "miro.medium.com" },
      { hostname: "liz-generic-files.s3.us-east-2.amazonaws.com" },
      { hostname: "flagcdn.com" },
    ],
  },
  output: "standalone",
  eslint: {
    ignoreDuringBuilds: true,
  },
  async rewrites() {
    const api = process.env.API;
    if (!api) {
      console.warn("API env var is not set. Skipping /api-oneepr rewrite.");
      return [];
    }

    const base = api.endsWith("/") ? api.slice(0, -1) : api;

    return [
      {
        source: "/api-oneepr/:path*",
        destination: `${base}/:path*`,
      },
    ];
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
